import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

import { AnimatePresence, motion } from 'framer-motion';
import { Plus, Save, Trash2, X } from 'lucide-react';
import { useRouter } from 'next/navigation';

import { But<PERSON> } from '@/components/ui/button';
import { ICompany, IJob } from '@/entities/interfaces';
import { CultureFitQuestions } from '@/entities/Job.entities';
import apiHelper from '@/lib/apiHelper';
import { useCompanyStore } from '@/stores/companyStore';
import { useMatchRankDetailsStore } from '@/stores/matchrankDetailsStore';
import { useJobsStore, useJobStateStore } from '@/stores/unifiedJobStore';
import { CommonJob, toCommonJob } from '@/types/common-job.types';
import { IconButton, Slider, TextField, Typography } from '@mui/material';

import { showToast } from '../Toaster';
import CulturalFitCard from './CulturalFitCard';
import QuestionSuggestions from './QuestionSuggestions';

interface CulturalFitDetailsDrawerProps {
  job: IJob;
  onClose?: () => void;
}

interface SaveButtonProps {
  onClick: () => void;
  disabled: boolean;
  hasUnsavedChanges: boolean;
  formActive: boolean;
  validationErrors: Record<string, string>;
}

const SaveButton: React.FC<SaveButtonProps> = ({
  onClick,
  disabled,
  hasUnsavedChanges,
  formActive,
  validationErrors,
}) => {
  return (
    <button
      type="button"
      onClick={onClick}
      disabled={disabled}
      className={`relative px-8 py-3 flex items-center gap-2 text-white rounded-full
        transition-all duration-300 font-semibold text-base
        ${
          hasUnsavedChanges && formActive && Object.keys(validationErrors).length === 0
            ? 'bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-500 hover:to-indigo-600 shadow-lg hover:shadow-xl scale-100 hover:scale-105'
            : 'bg-gray-500/50 text-gray-300 opacity-60 cursor-not-allowed'
        }`}
    >
      <Save
        size={20}
        style={{
          color:
            hasUnsavedChanges && formActive && Object.keys(validationErrors).length === 0
              ? 'var(--button-primary-text)'
              : 'var(--button-secondary-text)',
        }}
      />
      <span>Save Changes</span>
    </button>
  );
};

const CulturalFitDetailsDrawer = ({ job: initialJob, onClose }: CulturalFitDetailsDrawerProps) => {
  const router = useRouter();
  const { selectedJob, fetchJobById } = useJobsStore();
  const { company: storeCompany, fetchCompany } = useCompanyStore();
  const [job, setJob] = useState<CommonJob>(toCommonJob(initialJob));
  const [questions, setQuestions] = useState<CultureFitQuestions[]>(
    // Ensure there's at least one empty question by default
    initialJob.cultureFitQuestions?.length
      ? initialJob.cultureFitQuestions
      : [{ id: '1', question: '', duration: 1 }]
  );
  const [isSaving, setIsSaving] = useState(false);
  const [company, setCompany] = useState<ICompany | null>(null);
  const [isClosing, setIsClosing] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [initialQuestions, setInitialQuestions] = useState<CultureFitQuestions[]>([]);
  const [isDrawerVisible, setIsDrawerVisible] = useState(true);

  // Validation: Check if at least one question has content and no empty questions exist
  const validationErrors = React.useMemo(() => {
    const errors: Record<string, string> = {};
    const validQuestions = questions.filter(q => q.question.trim() !== '');
    const hasEmptyQuestions = questions.some(q => q.question.trim() === '');

    if (validQuestions.length === 0) {
      errors.questions = 'At least one question must have content';
    }

    if (hasEmptyQuestions && questions.length > 1) {
      errors.emptyQuestions = 'Please fill in all questions or remove empty ones';
    }

    return errors;
  }, [questions]);

  // Subscribe to job state changes using Zustand store
  useEffect(() => {
    // Subscribe to job state changes
    const unsubscribe = useJobStateStore.subscribe(
      state => state.jobs,
      jobs => {
        // Find the current job in the updated jobs
        const updatedJob = jobs.find(j => j.id === job.id);
        if (updatedJob) {
          // Use a targeted comparison to detect changes in cultural fit questions
          const currentJobKey = JSON.stringify({
            cultureFitQuestions: job.cultureFitQuestions,
            cultureFitDescription: job.cultureFitDescription,
            candidates: job.candidates?.length,
          });

          const updatedJobKey = JSON.stringify({
            cultureFitQuestions: updatedJob.cultureFitQuestions,
            cultureFitDescription: updatedJob.cultureFitDescription,
            candidates: updatedJob.candidates?.length,
          });

          if (currentJobKey !== updatedJobKey) {
            setJob(updatedJob);
            setQuestions(
              updatedJob.cultureFitQuestions?.length
                ? Array.isArray(updatedJob.cultureFitQuestions)
                  ? (updatedJob.cultureFitQuestions.filter(
                      q => typeof q === 'object' && 'id' in q
                    ) as CultureFitQuestions[])
                  : [{ id: '1', question: '', duration: 1 }]
                : [{ id: '1', question: '', duration: 1 }]
            );
          }
        }
      }
    );

    // Clean up subscription
    return () => {
      unsubscribe();
    };
  }, [job.id]);

  // Fetch company data when component mounts
  useEffect(() => {
    const fetchCompanyData = async () => {
      try {
        // Use the company store instead of direct API call
        const companyData = await fetchCompany();
        setCompany(companyData);
      } catch (error) {
        console.error('Error fetching company data:', error);
      }
    };

    // Use store data if available, otherwise fetch
    if (storeCompany) {
      setCompany(storeCompany);
    } else {
      fetchCompanyData();
    }
  }, [storeCompany, fetchCompany]);

  // Update local state when selectedJob changes
  useEffect(() => {
    if (selectedJob && selectedJob.id === job.id) {
      // Use a more targeted comparison to detect changes in cultural fit questions
      const currentJobKey = JSON.stringify({
        cultureFitQuestions: job.cultureFitQuestions,
        cultureFitDescription: job.cultureFitDescription,
        candidates: job.candidates?.length,
      });

      const selectedJobKey = JSON.stringify({
        cultureFitQuestions: selectedJob.cultureFitQuestions,
        cultureFitDescription: selectedJob.cultureFitDescription,
        candidates: selectedJob.candidates?.length,
      });

      if (currentJobKey !== selectedJobKey) {
        setJob(selectedJob);
        const cultureFitQuestions = selectedJob.cultureFitQuestions || [];
        const newQuestions =
          cultureFitQuestions.length > 0 && typeof cultureFitQuestions[0] === 'object'
            ? (cultureFitQuestions.filter(
                q => typeof q === 'object' && 'id' in q
              ) as CultureFitQuestions[])
            : [{ id: '1', question: '', duration: 1 }];
        setQuestions(newQuestions);
        setInitialQuestions(newQuestions);
        setHasUnsavedChanges(false);
      }
    }
  }, [selectedJob, job.id]);

  // Initialize initial questions when component mounts
  useEffect(() => {
    const currentQuestions = initialJob.cultureFitQuestions?.length
      ? initialJob.cultureFitQuestions
      : [{ id: '1', question: '', duration: 1 }];
    setInitialQuestions(currentQuestions);
  }, [initialJob]);

  // Track changes when questions array changes (add/remove)
  useEffect(() => {
    const hasChanges = JSON.stringify(questions) !== JSON.stringify(initialQuestions);
    setHasUnsavedChanges(hasChanges);
  }, [questions, initialQuestions]);

  useEffect(() => {
    const initializeJob = async () => {
      if (job.id) {
        setIsSaving(true);

        try {
          // Use the new culture fit details endpoint
          const cultureFitData = await apiHelper.get(`/jobs/${job.id}/culture-fit-details`);

          if (cultureFitData) {
            // Transform the data to match the expected structure
            const updatedJob = {
              ...job,
              ...cultureFitData.job,
              candidates: cultureFitData.candidates,
            };

            setJob(updatedJob);

            // Update questions from the job data
            if (cultureFitData.job.cultureFitQuestions?.length) {
              setQuestions(cultureFitData.job.cultureFitQuestions);
              setInitialQuestions(cultureFitData.job.cultureFitQuestions);
            }

            // Cache the fetched job
            useMatchRankDetailsStore.getState().cacheJob(updatedJob);
          }
        } catch (error) {
          console.error('Error fetching culture fit details:', error);
        } finally {
          setIsSaving(false);
        }
      }
    };

    initializeJob();
  }, [job.id]);

  const handleSave = async () => {
    setIsSaving(true);

    try {
      // Filter out questions with empty question text before sending to backend
      const validQuestions = questions.filter(q => q.question.trim() !== '');

      // Use the company's cultureFitDescription if available
      const cultureFitDescription = company?.cultureFitDescription || '';

      // Use the specialized culture fit endpoint for better debugging
      const { updateJobCultureFit } = useJobsStore.getState();
      const response = await updateJobCultureFit(job.id, {
        cultureFitQuestions: validQuestions,
        cultureFitDescription,
      });

      if (response && response.success) {
        // Use the verified job data from the response
        const updatedJob = response.data;

        // Update in all stores to ensure consistency
        useJobsStore.getState().setSelectedJob(updatedJob);
        useMatchRankDetailsStore.getState().setSelectedJob(updatedJob);
        useMatchRankDetailsStore.getState().cacheJob(updatedJob);

        // Update local state with the verified data
        setJob(updatedJob);
        const newQuestions = updatedJob.cultureFitQuestions?.length
          ? updatedJob.cultureFitQuestions
          : [{ id: '1', question: '', duration: 1 }];
        setQuestions(newQuestions);
        setInitialQuestions(newQuestions);
        setHasUnsavedChanges(false);

        // Update the job state store to notify other components
        useJobStateStore.getState().markJobAsUpdated(job.id);

        showToast({
          message: response.message || 'Culture fit questions updated successfully',
          isSuccess: true,
        });
      } else {
        showToast({
          message: response?.message || 'Failed to update culture fit questions',
          isSuccess: false,
        });
      }
    } catch (error) {
      console.error('Error saving culture fit settings:', error);
      showToast({
        message: 'Error updating culture fit questions',
        isSuccess: false,
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleQuestionChange = (
    index: number,
    field: keyof CultureFitQuestions,
    value: string | number
  ) => {
    const updatedQuestions = questions.map((q, i) => (i === index ? { ...q, [field]: value } : q));
    setQuestions(updatedQuestions);

    // Check if there are unsaved changes
    const hasChanges = JSON.stringify(updatedQuestions) !== JSON.stringify(initialQuestions);
    setHasUnsavedChanges(hasChanges);
  };

  const handleClose = async () => {
    // Prevent multiple close attempts
    if (isClosing) return;

    // Start the closing animation
    setIsClosing(true);

    // Update the job state store to notify other components about the update
    try {
      // Mark the job as updated in the job state store for seamless UI updates
      useJobStateStore.getState().markJobAsUpdated(job.id);
    } catch (error) {
      console.error('Error updating job state on modal close:', error);
    }

    // Call the original onClose handler immediately if provided
    if (onClose) {
      onClose(); // Call immediately, let the animation happen in the background
      return; // Let the parent handle navigation
    }

    // If no onClose handler, handle navigation ourselves after a short delay
    setTimeout(() => {
      // Clear the jobId parameter to prevent re-opening
      const currentPath = window.location.pathname;
      const searchParams = new URLSearchParams(window.location.search);
      searchParams.delete('jobId');

      const newUrl = `${currentPath}${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
      router.push(newUrl);
    }, 150); // Keep delay only for fallback navigation
  };

  const handleVideoModalOpen = () => {
    setIsDrawerVisible(false);
  };

  const handleVideoModalClose = () => {
    setIsDrawerVisible(true);
  };

  const content = (
    <div className="fixed inset-0 z-[9999]">
      <motion.div
        className="fixed inset-0 backdrop-blur-sm bg-[var(--sidebar-bg)]"
        onClick={handleClose}
        initial={{ opacity: 0 }}
        animate={{ opacity: isClosing ? 0 : 1 }}
        transition={{ duration: 0.15 }}
      />
      <AnimatePresence mode="wait">
        <motion.div
          key="culture-fit-details-drawer"
          className="fixed inset-y-0 right-0 w-full max-w-[75vw] shadow-xl z-[10000] flex flex-col backdrop-blur-lg bg-black/10 border-l border-white/5 text-[var(--foreground-color)]"
          initial={{ x: '100%' }}
          animate={{
            x: isClosing ? '100%' : isDrawerVisible ? 0 : '100%',
          }}
          exit={{ x: '100%' }}
          transition={{
            type: 'tween',
            duration: 0.15,
            ease: 'easeOut',
          }}
          onClick={e => e.stopPropagation()} // Prevent click from bubbling to backdrop
        >
          <div className="flex items-center justify-between p-6 border-b border-gray-300/30 dark:border-gray-300/30">
            <div>
              <h2 className="text-xl font-semibold text-[var(--foreground-color)]">
                Video Intro Questions
              </h2>
              <p className="mt-1 text-[var(--input-placeholder)]">
                {job.candidates?.length || 0} Candidates
              </p>
            </div>
            <button
              type="button"
              onClick={handleClose}
              className="p-2 rounded-full transition-colors bg-[var(--button-secondary-bg)] text-[var(--button-secondary-text)] relative z-[10001] cursor-pointer"
              aria-label="Close details"
            >
              <X className="w-6 h-6 text-[var(--input-placeholder)]" />
            </button>
          </div>

          <div className="flex flex-1 overflow-hidden">
            {/* Questions Section */}
            <div className="flex-1 overflow-y-auto p-6 border-r border-white/10 dark:border-gray-300/30">
              <div className="space-y-8">
                <div className="space-y-6">
                  <p className="text-lg font-semibold mb-4 text-[var(--foreground-color)]">
                    {`Successful Candidate's Questionnaire`}
                  </p>
                  {questions.map((q, index) => (
                    <div
                      key={index}
                      className="rounded-xl p-6 relative group border border-gray-300/30 dark:border-gray-300/30"
                    >
                      <div className="flex justify-between mb-4">
                        <Typography
                          className="font-semibold"
                          style={{ color: 'var(--foreground-color)' }}
                        >
                          Question: {index + 1}
                        </Typography>
                        <IconButton
                          onClick={() => {
                            const newQuestions = questions.filter((_, i) => i !== index);
                            setQuestions(newQuestions);
                          }}
                          disabled={questions.length === 1}
                          className="transition-opacity duration-200"
                          sx={{
                            color:
                              questions.length === 1
                                ? 'var(--input-placeholder)'
                                : 'var(--error-color)',
                            opacity: questions.length === 1 ? 0.5 : 1,
                            cursor: questions.length === 1 ? 'not-allowed' : 'pointer',
                            '&:hover': {
                              color:
                                questions.length === 1
                                  ? 'var(--input-placeholder)'
                                  : 'var(--error-color)',
                              opacity: questions.length === 1 ? 0.5 : 0.8,
                              backgroundColor:
                                questions.length === 1 ? 'transparent' : 'var(--error-bg)',
                            },
                          }}
                        >
                          <Trash2 className="w-5 h-5" />
                        </IconButton>
                      </div>

                      <TextField
                        fullWidth
                        value={q.question}
                        onChange={e => handleQuestionChange(index, 'question', e.target.value)}
                        placeholder={`Question ${index + 1}`}
                        className="mb-4"
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            color: 'var(--foreground-color)',
                            backgroundColor: 'var(--input-bg)',
                            '& fieldset': {
                              borderColor: 'var(--input-border)',
                            },
                            '&:hover fieldset': {
                              borderColor: 'var(--input-border)',
                              opacity: 0.8,
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: 'var(--button-primary-bg)',
                            },
                          },
                          '& .MuiInputLabel-root': {
                            color: 'var(--input-placeholder)',
                          },
                          '& .MuiInputBase-input': {
                            '&::placeholder': {
                              color: 'var(--input-placeholder)',
                              opacity: 0.7,
                            },
                          },
                        }}
                      />

                      {!q.question.trim() && (
                        <QuestionSuggestions
                          onSelectQuestion={question =>
                            handleQuestionChange(index, 'question', question)
                          }
                        />
                      )}

                      <Typography style={{ color: 'var(--input-placeholder)' }} className="mb-2">
                        Response Duration: {q.duration} minutes
                      </Typography>

                      <Slider
                        value={q.duration}
                        onChange={(_, value) =>
                          handleQuestionChange(index, 'duration', value as number)
                        }
                        step={0.5}
                        marks
                        min={0.5}
                        max={3}
                        valueLabelDisplay="auto"
                        sx={{
                          color: 'var(--button-primary-bg)',
                          height: 8,
                          '& .MuiSlider-track': {
                            border: 'none',
                            height: 8,
                            borderRadius: 4,
                            backgroundColor: 'var(--button-primary-bg)',
                          },
                          '& .MuiSlider-thumb': {
                            height: 20,
                            width: 20,
                            backgroundColor: 'var(--foreground-color)',
                            border: '2px solid currentColor',
                            '&:focus, &:hover, &.Mui-active': {
                              boxShadow: '0 0 0 8px rgba(124, 58, 237, 0.1)',
                            },
                          },
                          '& .MuiSlider-rail': {
                            height: 8,
                            borderRadius: 4,
                            backgroundColor: 'var(--button-secondary-bg)',
                            opacity: 0.8,
                          },
                          '& .MuiSlider-valueLabel': {
                            backgroundColor: 'var(--card-bg)',
                            color: 'var(--foreground-color)',
                            borderColor: 'var(--card-border)',
                            border: '1px solid var(--card-border)',
                            padding: '6px 12px',
                            borderRadius: '8px',
                            boxShadow: '0 2px 12px rgba(0, 0, 0, 0.1)',
                          },
                          '& .MuiSlider-mark': {
                            backgroundColor: 'var(--button-secondary-bg)',
                            height: 12,
                            width: 2,
                            marginTop: -2,
                          },
                          '& .MuiSlider-markActive': {
                            backgroundColor: 'var(--button-primary-bg)',
                          },
                        }}
                      />
                    </div>
                  ))}

                  <Button
                    onClick={() =>
                      setQuestions([
                        ...questions,
                        {
                          id: `${questions.length + 1}`,
                          question: '',
                          duration: 1,
                        },
                      ])
                    }
                    disabled={questions.some(q => q.question.trim() === '')}
                    className="w-full rounded-lg backdrop-blur-sm transition-all duration-300 flex items-center justify-center gap-2"
                    style={{
                      backgroundColor: questions.some(q => q.question.trim() === '')
                        ? 'var(--button-disabled-bg)'
                        : 'var(--button-secondary-bg)',
                      color: questions.some(q => q.question.trim() === '')
                        ? 'var(--button-disabled-text)'
                        : 'var(--button-secondary-text)',
                      border: '1px solid var(--card-border)',
                      opacity: questions.some(q => q.question.trim() === '') ? 0.5 : 1,
                      cursor: questions.some(q => q.question.trim() === '')
                        ? 'not-allowed'
                        : 'pointer',
                    }}
                  >
                    <Plus size={16} />
                    Add more Question
                  </Button>

                  <div className="flex justify-center">
                    <SaveButton
                      onClick={handleSave}
                      disabled={
                        isSaving || !hasUnsavedChanges || Object.keys(validationErrors).length > 0
                      }
                      hasUnsavedChanges={hasUnsavedChanges}
                      formActive={!isSaving}
                      validationErrors={validationErrors}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Candidates Section */}
            <div className="w-1/3 overflow-y-auto p-6 text-[var(--foreground-color)]">
              <h3 className="text-lg font-medium mb-4 text-[var(--foreground-color)]">
                Candidates
              </h3>
              <div className="space-y-4">
                {job.candidates && job.candidates.length > 0 ? (
                  job.candidates.map(candidate => (
                    <CulturalFitCard
                      key={candidate.id}
                      candidate={candidate}
                      job={job as IJob}
                      questionLength={job.cultureFitQuestions?.length || 0}
                      onVideoModalOpen={handleVideoModalOpen}
                      onVideoModalClose={handleVideoModalClose}
                    />
                  ))
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    No candidates found for this job
                  </div>
                )}
              </div>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>
    </div>
  );

  return typeof window === 'undefined' ? null : createPortal(content, document.body);
};

export default CulturalFitDetailsDrawer;
