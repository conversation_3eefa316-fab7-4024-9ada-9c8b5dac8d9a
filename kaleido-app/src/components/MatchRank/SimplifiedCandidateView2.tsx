'use client';

import StatusTimeline from '@/components/shared/StatusTimeline';
import { showToast } from '@/components/Toaster';
import { ICandidate } from '@/entities/interfaces';
import { useJobStore } from '@/stores/unifiedJobStore';
import { CandidateStatus } from '@/types/candidate.types';
import { motion } from 'framer-motion';
import React, { useState } from 'react';
import AnimatedScoreCard from './AnimatedScoreCard';
import { DetailedEvaluationModal } from './CandidateInfo/DetailedEvaluationModal';
import { ExperienceSection } from './CandidateInfo/ExperienceSection';
import { MatchReasoning } from './CandidateInfo/MatchReasoningModal';
import { SkillsSection } from './CandidateInfo/SkillsSection';

interface SimplifiedCandidateView2Props {
  candidate: ICandidate;
  jobId: string;
  onCandidateSelect?: (candidate: ICandidate) => void;
  onStatusUpdate?: () => void;
  topCandidateThreshold?: number;
  secondTierCandidateThreshold?: number;
}

type TabType = 'overview' | 'status' | 'detailed-analysis' | 'experience' | 'match-reasoning';

export const SimplifiedCandidateView2: React.FC<SimplifiedCandidateView2Props> = ({
  candidate,
  jobId,
  onCandidateSelect,
  onStatusUpdate,
  topCandidateThreshold = 80,
  secondTierCandidateThreshold = 60,
}) => {
  const [activeTab, setActiveTab] = useState<TabType>('overview');
  const [isShortlisting, setIsShortlisting] = useState(false);
  const [showDetailedAnalysis, setShowDetailedAnalysis] = useState(false);
  const [showMatchReasoning, setShowMatchReasoning] = useState(false);

  const jobStore = useJobStore();

  const handleShortlistToggle = async () => {
    if (isShortlisting || !jobId) return;

    setIsShortlisting(true);
    const isCurrentlyShortlisted = candidate.status === CandidateStatus.SHORTLISTED;
    const newStatus = isCurrentlyShortlisted ? CandidateStatus.NEW : CandidateStatus.SHORTLISTED;

    try {
      // Use optimistic update
      jobStore.updateCandidateStatusOptimistic(jobId, candidate.id, newStatus);

      showToast({
        message: isCurrentlyShortlisted ? 'Removed from shortlist' : 'Added to shortlist',
        isSuccess: true,
      });

      onStatusUpdate?.();
    } catch (error) {
      console.error('Error updating shortlist status:', error);
      showToast({
        message: 'Failed to update shortlist status',
        isSuccess: false,
      });
    } finally {
      setIsShortlisting(false);
    }
  };

  const tabs = [
    { id: 'overview' as const, label: 'Overview' },
    { id: 'status' as const, label: 'Status' },
    { id: 'detailed-analysis' as const, label: 'Detailed Analysis' },
    { id: 'experience' as const, label: 'Experience' },
    { id: 'match-reasoning' as const, label: 'Match Reasoning' },
  ];

  const handleDownload = () => {
    // TODO: Implement download functionality
    showToast({ message: 'Download feature coming soon', type: 'info' });
  };

  const handleShare = () => {
    // TODO: Implement share functionality
    showToast({ message: 'Share feature coming soon', type: 'info' });
  };

  return (
    <div className="h-full flex flex-col bg-background text-foreground">
      {/* Nothing here - tabs will be in the parent component */}

      {/* Tab Content */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-6">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.2 }}
          >
            {activeTab === 'overview' && (
              <div className="max-w-6xl mx-auto">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Left Column - Candidate Information */}
                  <div className="space-y-6">
                    <div className="bg-card rounded-lg p-6 border border-border">
                      <h3 className="text-lg font-semibold mb-4">Candidate Information</h3>

                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm text-muted-foreground">Full Name</label>
                            <p className="font-medium">{candidate.fullName}</p>
                          </div>
                          <div>
                            <label className="text-sm text-muted-foreground">Current Title</label>
                            <p className="font-medium">{candidate.jobTitle}</p>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm text-muted-foreground">Location</label>
                            <p className="font-medium">{candidate.location || 'Not specified'}</p>
                          </div>
                          <div>
                            <label className="text-sm text-muted-foreground">
                              Total Experience
                            </label>
                            <p className="font-medium">
                              {(candidate as any).yearsOfExperience
                                ? `${(candidate as any).yearsOfExperience} Years`
                                : 'Not specified'}
                            </p>
                          </div>
                        </div>

                        {(candidate as any).email && (
                          <div>
                            <label className="text-sm text-muted-foreground">Email</label>
                            <p className="font-medium">{(candidate as any).email}</p>
                          </div>
                        )}

                        {(candidate as any).phone && (
                          <div>
                            <label className="text-sm text-muted-foreground">Phone</label>
                            <p className="font-medium">{(candidate as any).phone}</p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Additional sections */}
                    {candidate.skills && candidate.skills.length > 0 && (
                      <SkillsSection skills={candidate.skills} />
                    )}
                  </div>

                  {/* Right Column - Match Score */}
                  <div className="flex items-start justify-center">
                    <AnimatedScoreCard
                      candidate={candidate as any}
                      matchScore={candidate.evaluation?.matchScore}
                      rank={candidate.evaluation?.rank}
                      topCandidateThreshold={topCandidateThreshold}
                      secondTierCandidateThreshold={secondTierCandidateThreshold}
                    />
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'status' && (
              <div className="max-w-4xl mx-auto">
                <div className="rounded-xl p-6 border bg-card border-border">
                  <h3 className="text-lg font-semibold mb-6">Application Status Timeline</h3>
                  <StatusTimeline
                    currentStatus={candidate.status as CandidateStatus}
                    onStatusClick={newStatus => {
                      // Handle status update
                    }}
                    size="lg"
                    showAllStatuses={true}
                  />
                </div>
              </div>
            )}

            {activeTab === 'detailed-analysis' && (
              <div className="max-w-4xl mx-auto">
                <div className="rounded-xl p-6 border bg-card border-border">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-lg font-semibold">Detailed Analysis</h3>
                    <button
                      onClick={() => setShowDetailedAnalysis(true)}
                      className="px-4 py-2 rounded-lg bg-primary hover:bg-primary/80 text-primary-foreground transition-colors text-sm font-medium"
                    >
                      View Full Analysis
                    </button>
                  </div>

                  {candidate.evaluation && (
                    <div className="space-y-4">
                      {/* Match Score Summary */}
                      <div className="p-4 rounded-lg bg-muted">
                        <h4 className="font-medium mb-2">Match Score</h4>
                        <p className="text-2xl font-bold text-primary">
                          {Math.round(
                            candidate.evaluation.matchScore > 1
                              ? candidate.evaluation.matchScore
                              : candidate.evaluation.matchScore * 100
                          )}
                          %
                        </p>
                      </div>

                      {/* Criteria Breakdown */}
                      {candidate.evaluation.detailedScoreAnalysis?.specificCriteriaMatched && (
                        <div className="space-y-3">
                          <h4 className="font-medium">Criteria Breakdown</h4>
                          {Object.entries(
                            candidate.evaluation.detailedScoreAnalysis.specificCriteriaMatched
                          ).map(([key, value]) => (
                            <div
                              key={key}
                              className="flex items-center justify-between p-3 rounded-lg bg-muted"
                            >
                              <span className="text-sm capitalize">
                                {key.replace(/([A-Z])/g, ' $1').trim()}
                              </span>
                              <span className="font-medium">
                                {Math.round(
                                  (value as number) > 1
                                    ? (value as number)
                                    : (value as number) * 100
                                )}
                                %
                              </span>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'experience' && (
              <ExperienceSection
                experiences={candidate.experience || []}
                currentPage={0}
                totalPages={1}
                onPageChange={() => {}}
              />
            )}
          </motion.div>
        </div>
      </div>

      {/* Detailed Evaluation Modal */}
      {candidate.evaluation && (
        <DetailedEvaluationModal
          open={showDetailedAnalysis}
          onOpenChange={setShowDetailedAnalysis}
          evaluation={candidate.evaluation}
        />
      )}

      {/* Match Reasoning Modal */}
      {showMatchReasoning && candidate.evaluation?.yourReasoningForScoring && (
        <MatchReasoning reasoningText={candidate.evaluation.yourReasoningForScoring} />
      )}
    </div>
  );
};
