'use client';

import { Suspense } from 'react';
import { usePathname } from 'next/navigation';
import { JobsProvider } from '@/contexts/jobs/JobsContext';

interface ConditionalJobsProviderProps {
  children: React.ReactNode;
}

function ConditionalJobsProviderInner({ children }: ConditionalJobsProviderProps) {
  const pathname = usePathname();
  
  // Only wrap with JobsProvider for job creation related paths
  const shouldUseJobsProvider = pathname?.includes('/job-description-creation') || 
                               pathname?.includes('/job-create') ||
                               pathname?.includes('/job-edit');
  
  if (shouldUseJobsProvider) {
    return <JobsProvider>{children}</JobsProvider>;
  }
  
  return <>{children}</>;
}

export default function ConditionalJobsProvider({ children }: ConditionalJobsProviderProps) {
  return (
    <Suspense fallback={<>{children}</>}>
      <ConditionalJobsProviderInner>{children}</ConditionalJobsProviderInner>
    </Suspense>
  );
}