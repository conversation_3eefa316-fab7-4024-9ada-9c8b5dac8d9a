import { Repository } from 'typeorm';

import { ActivityType } from '@/shared/types/activity.types';
import { JobStatus } from '@/shared/types/job.types';
import { Candidate } from '@modules/candidate/entities/candidate.entity';
import { ApplicationStatus, CandidateStatus } from '@shared/types';
import { getCandidateJobWhereClause } from '@shared/utils/candidate-job-query.util';

import { JobApplication } from '../job-seeker/entities/job-application.entity';
import { Job } from './entities/job.entity';

export interface JobStats {
  totalJobs: number;
  jobsByStatus: Record<string, number>;
  jobsWithVideoJDs: number;
  jobsWithCandidates: number;
  departmentDistribution: { name: string; value: number }[];
  timeFrame: 'daily' | 'weekly' | 'monthly' | 'yearly';
}

export interface JobsByStatusResponse {
  data: Job[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
  stats?: JobStats; // Add statistics to the response
}

// Get candidate statistics using database aggregation (no entity loading)
async function getCandidateStatsForJobs(
  jobRepository: Repository<Job>,
  jobIds: string[],
): Promise<Record<string, any>> {
  if (jobIds.length === 0) return {};

  // Get candidate counts and statistics in a single aggregated query
  // Include candidates that have job in appliedJobs array
  const candidateStatsQuery = `
    SELECT
      COALESCE(c."jobId", j.job_id) as "jobId",
      COUNT(DISTINCT c.id) as totalCandidates,
      COUNT(DISTINCT CASE WHEN c."hasCompletedVideoInterview" = true THEN c.id END) as completedVideoInterviews,
      COUNT(DISTINCT CASE WHEN c.contacted = true THEN c.id END) as contactedCandidates,
      COUNT(DISTINCT CASE WHEN c.evaluation IS NULL OR (c.evaluation->>'matchScore') IS NULL THEN c.id END) as pendingEvaluation
    FROM candidates c
    CROSS JOIN LATERAL (
      SELECT unnest(ARRAY[$1::uuid[]]) as job_id
    ) j
    WHERE c."jobId" = j.job_id OR j.job_id = ANY(c."appliedJobs")
    GROUP BY COALESCE(c."jobId", j.job_id)
  `;

  const candidateResults = await jobRepository.query(candidateStatsQuery, [jobIds]);

  // Get tier counts separately since they depend on job thresholds
  const tierStatsQuery = `
    SELECT
      j.id as jobId,
      j."topCandidateThreshold",
      j."secondTierCandidateThreshold",
      COUNT(CASE
        WHEN (c.evaluation->>'matchScore')::float >= j."topCandidateThreshold" THEN 1
      END) as topTierCount,
      COUNT(CASE
        WHEN (c.evaluation->>'matchScore')::float >= j."secondTierCandidateThreshold"
        AND (c.evaluation->>'matchScore')::float < j."topCandidateThreshold" THEN 1
      END) as secondTierCount
    FROM jobs j
    LEFT JOIN candidates c ON c."jobId" = j.id
    WHERE j.id = ANY($1)
    AND j."topCandidateThreshold" IS NOT NULL
    AND j."secondTierCandidateThreshold" IS NOT NULL
    AND c.evaluation IS NOT NULL
    AND (c.evaluation->>'matchScore') IS NOT NULL
    GROUP BY j.id, j."topCandidateThreshold", j."secondTierCandidateThreshold"
  `;

  const tierResults = await jobRepository.query(tierStatsQuery, [jobIds]);

  // Combine results into a lookup object
  const statsLookup: Record<string, any> = {};

  // Initialize with candidate stats
  candidateResults.forEach((row: any) => {
    statsLookup[row.jobId] = {
      totalCandidates: parseInt(row.totalcandidates) || 0,
      completedVideoInterviews: parseInt(row.completedvideointerviews) || 0,
      contactedCandidates: parseInt(row.contactedcandidates) || 0,
      pendingEvaluation: parseInt(row.pendingevaluation) || 0,
      topTierCount: 0,
      secondTierCount: 0,
    };
  });

  // Add tier stats
  tierResults.forEach((row: any) => {
    if (statsLookup[row.jobId]) {
      statsLookup[row.jobId].topTierCount = parseInt(row.toptiercount) || 0;
      statsLookup[row.jobId].secondTierCount = parseInt(row.secondtiercount) || 0;
    }
  });
  return statsLookup;
}

export async function getJobsByStatusHelper(
  jobRepository: Repository<Job>,
  clientId: string,
  options: { page: number; limit: number; status?: string; includeStats?: boolean },
): Promise<JobsByStatusResponse> {
  const { page, limit, status, includeStats = false } = options;
  const skip = (page - 1) * limit;

  // Build the base query
  let jobsQuery = jobRepository
    .createQueryBuilder('job')
    .where('job.clientId = :clientId', { clientId });

  // Apply status filter if provided and not 'ALL'
  if (status && status !== 'ALL') {
    jobsQuery = jobsQuery.andWhere('job.status = :status', { status: status.toUpperCase() });
  }

  // Get total count for pagination
  const totalCount = await jobsQuery.getCount();

  // If there are no jobs, return an empty response with proper pagination
  if (totalCount === 0) {
    return {
      data: [],
      pagination: {
        currentPage: page,
        totalPages: 0,
        totalItems: 0,
        itemsPerPage: limit,
      },
      stats: includeStats
        ? {
            totalJobs: 0,
            jobsByStatus: {},
            jobsWithVideoJDs: 0,
            jobsWithCandidates: 0,
            departmentDistribution: [],
            timeFrame: 'weekly',
          }
        : undefined,
    };
  }

  // Only select job fields and videoJDs - we'll get candidate counts separately
  jobsQuery = jobsQuery
    .select([
      'job.id',
      'job.jobType',
      'job.department',
      'job.updatedAt',
      'job.status',
      'job.topCandidateThreshold',
      'job.secondTierCandidateThreshold',
      'job.cultureFitQuestions',
      'job.isPublished',
    ])
    .leftJoinAndSelect('job.videoJDs', 'videoJDs')
    .orderBy('job.updatedAt', 'DESC')
    .skip(skip)
    .take(limit);

  // Execute the query
  const jobs = await jobsQuery.getMany();

  // Get candidate statistics for all jobs in a single query
  const jobIds = jobs.map((job) => job.id);
  const candidateStats = await getCandidateStatsForJobs(jobRepository, jobIds);

  // Process the fetched jobs with aggregated stats
  const processedJobs = jobs.map((job) => {
    const stats = candidateStats[job.id] || {
      totalCandidates: 0,
      topTierCount: 0,
      secondTierCount: 0,
      pendingEvaluation: 0,
      completedVideoInterviews: 0,
      contactedCandidates: 0,
    };

    // Get latest video JD status
    const latestVideoJD = job.videoJDs?.sort(
      (a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime(),
    )[0];

    // Check if job has any video with videoUrl
    const hasVideoUrl =
      job.videoJDs?.some((videoJD) => videoJD.videoUrl && videoJD.videoUrl.trim() !== '') || false;

    // Add minimal stats to job
    const processedJob = Object.assign(job, {
      hasVideoUrl,
      candidateStats: {
        totalCandidates: stats.totalCandidates,
      },
      actionStats: {
        videoJD: {
          status: latestVideoJD?.status || 'NOT_CREATED',
          url: latestVideoJD?.videoUrl,
          count: job.videoJDs?.length || 0,
        },
        matchAndRank: {
          totalCandidates: stats.totalCandidates,
          topTierCount: stats.topTierCount,
          secondTierCount: stats.secondTierCount,
          pendingEvaluation: stats.pendingEvaluation,
          evaluationRate:
            stats.totalCandidates > 0
              ? Math.round(
                  ((stats.totalCandidates - stats.pendingEvaluation) / stats.totalCandidates) * 100,
                )
              : 0,
        },
        cultureFit: {
          completedInterviews: stats.completedVideoInterviews,
          totalCandidates: stats.totalCandidates,
          completionRate:
            stats.totalCandidates > 0
              ? Math.round((stats.completedVideoInterviews / stats.totalCandidates) * 100)
              : 0,
          contactedCandidates: stats.contactedCandidates,
          contactRate:
            stats.totalCandidates > 0
              ? Math.round((stats.contactedCandidates / stats.totalCandidates) * 100)
              : 0,
        },
      },
    });

    return processedJob;
  });

  // Calculate pagination info
  const totalPages = Math.ceil(totalCount / limit);

  // Create the response object
  const response: JobsByStatusResponse = {
    data: processedJobs,
    pagination: {
      currentPage: page,
      totalPages,
      totalItems: totalCount,
      itemsPerPage: limit,
    },
  };

  // Calculate and include statistics if requested
  if (includeStats) {
    // Get minimal data for statistics calculation (without pagination)
    const allJobs = await jobRepository
      .createQueryBuilder('job')
      .select(['job.id', 'job.status'])
      .leftJoin('job.candidates', 'candidates')
      .leftJoin('job.videoJDs', 'videoJDs')
      .addSelect(['candidates.id'])
      .addSelect(['videoJDs.id'])
      .where('job.clientId = :clientId', { clientId })
      .getMany();

    // Calculate job statistics
    const jobStatuses = Object.values(JobStatus);
    const statusCounts: Record<string, number> = {};

    // Get counts for each status
    for (const statusValue of jobStatuses) {
      const count = allJobs.filter((job) => job.status === statusValue).length;
      statusCounts[statusValue] = count;
    }

    // Count jobs with video JDs
    const jobsWithVideoJDs = allJobs.filter(
      (job) => job.videoJDs && job.videoJDs.length > 0,
    ).length;

    // Count jobs with candidates
    const jobsWithCandidates = allJobs.filter(
      (job) => job.candidates && job.candidates.length > 0,
    ).length;

    // Calculate department distribution
    const departments: Record<string, number> = {};
    allJobs.forEach((job) => {
      const dept = job.department || 'Other';
      departments[dept] = (departments[dept] || 0) + 1;
    });

    const departmentDistribution = Object.entries(departments)
      .map(([name, value]) => ({ name, value }))
      .sort((a, b) => b.value - a.value) // Sort by count (descending)
      .slice(0, 6); // Take top 6 departments

    // Add statistics to the response
    response.stats = {
      totalJobs: allJobs.length,
      jobsByStatus: statusCounts,
      jobsWithVideoJDs,
      jobsWithCandidates,
      departmentDistribution,
      timeFrame: 'weekly', // Default timeframe
    };
  }

  return response;
}

export async function getAllMatchedCandidatesHelper(
  jobRepository: Repository<Job>,
  clientId: string,
  pagination: { page?: number; limit?: number } = {},
) {
  // Ensure pagination values are numbers with defaults
  const page = Number(pagination.page) || 1;
  const limit = Number(pagination.limit) || 9;
  const offset = (page - 1) * limit;

  // Base query builder
  const baseQuery = jobRepository
    .createQueryBuilder('job')
    .leftJoinAndSelect('job.candidates', 'candidate')
    .where('job.clientId = :clientId', { clientId })
    .andWhere('candidate.id IS NOT NULL')
    .andWhere('candidate.evaluation IS NOT NULL'); // Only get candidates with evaluations

  // Get total count for pagination
  const totalCount = await baseQuery.getCount();

  // Get jobs with pagination
  const jobs = await jobRepository
    .createQueryBuilder('job')
    .leftJoinAndSelect('job.candidates', 'candidate')
    .where('job.clientId = :clientId', { clientId })
    .andWhere('candidate.id IS NOT NULL')
    .andWhere('candidate.evaluation IS NOT NULL')
    .orderBy('job.updatedAt', 'DESC')
    .skip(offset)
    .take(limit)
    .getMany();

  const data = jobs.map((job) => {
    const candidates = job.candidates || [];
    const evaluatedCandidates = candidates.filter((c) => c.evaluation);

    // Calculate match score statistics
    const matchScores = evaluatedCandidates.map((c) => {
      const rawScore = c.evaluation?.matchScore || 0;
      // If score is already in 0-100 range, use it directly, otherwise convert from decimal
      return rawScore > 1 ? Math.min(100, rawScore) : Math.min(100, rawScore * 100);
    });
    const averageMatchScore =
      matchScores.length > 0
        ? matchScores.reduce((acc, curr) => acc + curr, 0) / matchScores.length
        : 0;

    // Count candidates by status
    const candidatesByStatus: Record<CandidateStatus, number> = candidates.reduce(
      (acc, curr) => {
        acc[curr.status] = (acc[curr.status] || 0) + 1;
        return acc;
      },
      {} as Record<CandidateStatus, number>,
    );

    // Calculate evaluation tiers (only if thresholds are set on the job)
    let topTierCandidates: any[] = [];
    let secondTierCandidates: any[] = [];

    if (job.topCandidateThreshold !== undefined && job.secondTierCandidateThreshold !== undefined) {
      const topThreshold = Math.round(
        job.topCandidateThreshold > 1 ? job.topCandidateThreshold : job.topCandidateThreshold * 100,
      );
      const secondThreshold = Math.round(
        job.secondTierCandidateThreshold > 1
          ? job.secondTierCandidateThreshold
          : job.secondTierCandidateThreshold * 100,
      );

      topTierCandidates = evaluatedCandidates.filter((c) => {
        const rawScore = c.evaluation?.matchScore ?? 0;
        const normalizedScore =
          rawScore > 1 ? Math.min(100, rawScore) : Math.min(100, rawScore * 100);
        return normalizedScore >= topThreshold;
      });

      secondTierCandidates = evaluatedCandidates.filter((c) => {
        const rawScore = c.evaluation?.matchScore ?? 0;
        const normalizedScore =
          rawScore > 1 ? Math.min(100, rawScore) : Math.min(100, rawScore * 100);
        return normalizedScore >= secondThreshold && normalizedScore < topThreshold;
      });
    }

    // Calculate contacted vs not contacted
    const contactedCandidates = candidates.filter((c) => c.contacted).length;
    const notContactedCandidates = candidates.filter((c) => !c.contacted).length;

    // Calculate video interview completion rate
    const completedVideoInterviews = candidates.filter((c) => c.hasCompletedVideoInterview).length;

    return {
      id: job.id,
      jobTitle: job.jobType,
      company: job.companyName,
      department: job.department,
      location: job.location,
      candidates: {
        total: candidates.length,
        evaluated: evaluatedCandidates.length,
        byStatus: candidatesByStatus,
        topTier: topTierCandidates.length,
        secondTier: secondTierCandidates.length,
        contacted: contactedCandidates,
        notContacted: notContactedCandidates,
        completedVideoInterviews,
      },
      matchScores: {
        average: averageMatchScore.toFixed(1),
        distribution: {
          excellent: matchScores.filter((score) => score >= 90).length,
          veryGood: matchScores.filter((score) => score >= 80 && score < 90).length,
          good: matchScores.filter((score) => score >= 70 && score < 80).length,
          fair: matchScores.filter((score) => score >= 50 && score < 70).length,
          poor: matchScores.filter((score) => score < 50).length,
        },
      },
      thresholds:
        job.topCandidateThreshold !== undefined && job.secondTierCandidateThreshold !== undefined
          ? {
              topTier: Math.round(
                job.topCandidateThreshold > 1
                  ? job.topCandidateThreshold
                  : job.topCandidateThreshold * 100,
              ),
              secondTier: Math.round(
                job.secondTierCandidateThreshold > 1
                  ? job.secondTierCandidateThreshold
                  : job.secondTierCandidateThreshold * 100,
              ),
            }
          : undefined,
    };
  });

  return {
    data,
    pagination: {
      currentPage: page,
      totalPages: Math.ceil(totalCount / limit),
      totalItems: totalCount,
      itemsPerPage: limit,
    },
  };
}

export async function getJobApplicationStatusForCandidate(
  jobId: string,
  clientId: string,
  jobApplicationRepository: Repository<JobApplication>,
  candidateRepository: Repository<Candidate>,
) {
  // First, we need to find the job seeker to get their ID
  // Import the JobSeeker repository to find the job seeker
  const jobSeekerRepository = jobApplicationRepository.manager.getRepository('JobSeeker');

  let jobSeeker = await jobSeekerRepository.findOne({ where: { clientId } });
  if (!jobSeeker) {
    // Fallback: try to find by userId in case clientId is not set properly
    jobSeeker = await jobSeekerRepository.findOne({ where: { userId: clientId } });
  }

  if (!jobSeeker) {
    return {
      jobId,
      status: JobStatus.APPLIED,
      lastUpdated: new Date().toISOString(),
      statusTimeline: [],
    };
  }

  // Find the job application for this specific job and job seeker
  // Include soft-deleted (withdrawn) applications
  // Use createQueryBuilder to avoid column name issues and be explicit about columns
  const application = await jobApplicationRepository
    .createQueryBuilder('applications')
    .select([
      'applications.id',
      'applications.jobId',
      'applications.jobSeekerId',
      'applications.clientId',
      'applications.status',
      'applications.createdAt',
      'applications.updatedAt',
      'applications.deletedAt',
      'applications.withdrawalReason',
      // Don't select applicationReason as it might not exist in the database yet
    ])
    .where('applications.jobId = :jobId', { jobId })
    .andWhere('applications.jobSeekerId = :jobSeekerId', { jobSeekerId: jobSeeker.id })
    .withDeleted() // Include soft-deleted applications
    .getOne();

  if (!application) {
    return {
      jobId,
      status: JobStatus.APPLIED,
      lastUpdated: new Date().toISOString(),
      statusTimeline: [],
    };
  }

  // Rest of method remains the same
  // Find the candidate record for this job seeker and job with activity history
  // Use createQueryBuilder to ensure we get all fields including activityHistory
  // Include candidates that have the job in their appliedJobs array
  const candidate = await candidateRepository
    .createQueryBuilder('candidate')
    .where(getCandidateJobWhereClause(), { jobId })
    .getOne();

  // If we can't find a candidate by jobId and clientId, try to find by clientId only
  let fallbackCandidate = null;
  if (!candidate) {
    const candidates = await candidateRepository
      .createQueryBuilder('candidate')
      .where(getCandidateJobWhereClause(), { jobId })
      .orderBy('candidate.updatedAt', 'DESC') // Get the most recently updated candidates first
      .getMany();

    // Try to find a candidate with activity history
    const candidatesWithHistory = candidates.filter(
      (c) => c.activityHistory && c.activityHistory.length > 0,
    );
    if (candidatesWithHistory.length > 0) {
      // Sort by most recent activity
      candidatesWithHistory.sort((a, b) => {
        const aActivity = a.activityHistory || [];
        const bActivity = b.activityHistory || [];

        if (aActivity.length === 0) return 1;
        if (bActivity.length === 0) return -1;

        // Get the most recent activity for each candidate
        const aLatest = [...aActivity].sort(
          (x, y) => new Date(y.timestamp).getTime() - new Date(x.timestamp).getTime(),
        )[0];
        const bLatest = [...bActivity].sort(
          (x, y) => new Date(y.timestamp).getTime() - new Date(x.timestamp).getTime(),
        )[0];

        // Compare timestamps
        return new Date(bLatest.timestamp).getTime() - new Date(aLatest.timestamp).getTime();
      });

      // Use the candidate with the most recent status update
      fallbackCandidate = candidatesWithHistory[0];
    }
  }

  // Use either the found candidate or the fallback candidate
  const effectiveCandidate = candidate || fallbackCandidate;
  // Get the activity history from the candidate record
  const activityHistory = effectiveCandidate?.activityHistory || [];

  // Also get status changes specifically for tracking the current status
  const jobSpecificStatusChanges = activityHistory.filter(
    (activity) => activity.type === ActivityType.STATUS_CHANGED,
  );

  // Determine the current status and create a simplified timeline
  let actualStatus = application.status || (application.deletedAt ? 'WITHDRAWN' : 'APPLIED');
  let statusTimeline = [];

  // Format the activity history for the frontend
  const formattedActivityHistory = activityHistory.map((activity) => ({
    id: activity.id,
    type: activity.type,
    metadata: activity.metadata || {},
    timestamp: activity.timestamp,
    description: activity.description || '',
    performedBy: activity.performedBy || '',
  }));

  // If we have status changes in the activity history, use those to build the timeline
  if (jobSpecificStatusChanges.length > 0) {
    // Sort by timestamp to get the most recent status change
    const sortedChanges = [...jobSpecificStatusChanges].sort(
      (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
    );

    // Use the most recent status change for current status
    const latestChange = sortedChanges[0];
    actualStatus = latestChange.metadata?.newStatus || actualStatus;

    // Convert status changes to a map to deduplicate by status
    const statusMap = new Map();

    // Process all status changes
    jobSpecificStatusChanges.forEach((activity) => {
      const newStatus = activity.metadata?.newStatus;
      const prevStatus = activity.metadata?.previousStatus;

      if (newStatus) {
        statusMap.set(newStatus, {
          status: newStatus,
          timestamp: activity.timestamp,
          note: activity.description || activity.metadata?.notes || '',
        });
      }

      // Add previous status if it exists and is not already in the map
      if (prevStatus && prevStatus !== CandidateStatus.NEW && !statusMap.has(prevStatus)) {
        const prevTimestamp = new Date(new Date(activity.timestamp).getTime() - 1000).toISOString();
        statusMap.set(prevStatus, {
          status: prevStatus,
          timestamp: prevTimestamp,
          note: `Initial status before changing to ${newStatus}`,
        });
      }
    });

    // Make sure APPLIED status is present with correct note if it doesn't exist
    if (!statusMap.has('APPLIED')) {
      statusMap.set('APPLIED', {
        status: 'APPLIED',
        timestamp: application.createdAt,
        note: 'Application submitted',
      });
    } else if (statusMap.has('APPLIED') && !statusMap.get('APPLIED').note) {
      const appliedEntry = statusMap.get('APPLIED');
      appliedEntry.note = 'Application submitted';
      statusMap.set('APPLIED', appliedEntry);
    }

    // Convert to array and sort by timestamp
    statusTimeline = Array.from(statusMap.values()).sort(
      (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime(),
    );
  }
  // If no specific status changes but we have an application, create a simple timeline
  else if (application) {
    // If application was withdrawn, show both APPLIED and WITHDRAWN statuses
    if (application.deletedAt) {
      statusTimeline = [
        {
          status: 'APPLIED',
          timestamp: application.createdAt,
          note: 'Application submitted',
        },
        {
          status: 'WITHDRAWN',
          timestamp: application.deletedAt,
          note: application.withdrawalReason || 'Application withdrawn',
        },
      ];
      actualStatus = ApplicationStatus.WITHDRAWN;
    }
    // Otherwise just show the application status
    else {
      statusTimeline = [
        {
          status: application.status,
          timestamp: application.createdAt,
          note: 'Application submitted',
        },
      ];
      actualStatus = application.status;
    }
  }

  return {
    jobId,
    status: actualStatus,
    lastUpdated:
      effectiveCandidate?.updatedAt?.toISOString() || application.updatedAt.toISOString(),
    statusTimeline,
    withdrawn: application.deletedAt ? true : false,
    activityHistory: formattedActivityHistory,
  };
}
