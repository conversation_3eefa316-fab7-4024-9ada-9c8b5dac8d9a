'use client';

import React, { useEffect, useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowRight,
  Plus,
  Clock,
  ChevronDown,
  ChevronRight,
  Eye,
  Calendar,
  TrendingUp,
  Star,
  Archive,
  Sparkles,
} from 'lucide-react';
import { useRouter } from 'next/navigation';

import SimpleJobSeekerLayout from '@/components/steps/layout/SimpleJobSeekerLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useCareerInsightsStore } from '@/stores/careerInsightsStore';
import ColourfulLoader from '@/components/Layouts/ColourfulLoader';
import { UserRole } from '@/types/roles';
import { InsightType, insightTypeConfig, InsightStatus } from '@/lib/career-insights/config';
import { cn } from '@/lib/utils';

interface GroupedInsights {
  [key: string]: {
    latest: any;
    history: any[];
    totalViews: number;
  };
}

export default function GroupedCareerInsightsPage() {
  const router = useRouter();
  const { insights, isLoading, fetchInsights } = useCareerInsightsStore();
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());

  useEffect(() => {
    fetchInsights();
  }, [fetchInsights]);

  // Group insights by type and sort by creation date
  const groupedInsights = useMemo(() => {
    const groups: GroupedInsights = {};

    insights.forEach(insight => {
      if (!groups[insight.type]) {
        groups[insight.type] = {
          latest: null,
          history: [],
          totalViews: 0,
        };
      }

      groups[insight.type].totalViews += insight.viewCount || 0;

      if (
        !groups[insight.type].latest ||
        new Date(insight.createdAt) > new Date(groups[insight.type].latest.createdAt)
      ) {
        if (groups[insight.type].latest) {
          groups[insight.type].history.push(groups[insight.type].latest);
        }
        groups[insight.type].latest = insight;
      } else {
        groups[insight.type].history.push(insight);
      }
    });

    // Sort history by date descending
    Object.values(groups).forEach(group => {
      group.history.sort(
        (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
    });

    return groups;
  }, [insights]);

  const toggleGroup = (type: string) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(type)) {
      newExpanded.delete(type);
    } else {
      newExpanded.add(type);
    }
    setExpandedGroups(newExpanded);
  };

  const handleCreateInsight = (type: InsightType) => {
    router.push(`/career-insights/create?type=${type}`);
  };

  const handleViewInsight = (id: string) => {
    router.push(`/career-insights/${id}`);
  };

  const getStatusBadge = (status: InsightStatus) => {
    const statusConfig = {
      [InsightStatus.READY]: {
        label: 'Ready',
        className: 'bg-green-500/20 text-green-400 border-green-500/30',
      },
      [InsightStatus.PROCESSING]: {
        label: 'Processing',
        className: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
      },
      [InsightStatus.DRAFT]: {
        label: 'Draft',
        className: 'bg-gray-500/20 text-gray-400 border-gray-500/30',
      },
      [InsightStatus.ARCHIVED]: {
        label: 'Archived',
        className: 'bg-red-500/20 text-red-400 border-red-500/30',
      },
    };

    const config = statusConfig[status] || statusConfig[InsightStatus.DRAFT];
    return (
      <Badge variant="outline" className={cn('text-xs', config.className)}>
        {config.label}
      </Badge>
    );
  };

  const formatTimeAgo = (date: string) => {
    const now = new Date();
    const then = new Date(date);
    const diffInMs = now.getTime() - then.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    if (diffInDays < 7) return `${diffInDays} days ago`;
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`;
    if (diffInDays < 365) return `${Math.floor(diffInDays / 30)} months ago`;
    return `${Math.floor(diffInDays / 365)} years ago`;
  };

  return (
    <SimpleJobSeekerLayout>
      <div className="flex-1 px-4 py-6 md:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-2">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">Career Insights</h1>
              <p className="text-gray-300">AI-powered insights to accelerate your career growth</p>
            </div>
            <Button
              onClick={() =>
                router.push(`/career-insights/create?type=${InsightType.SKILL_GAP_ANALYSIS}`)
              }
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
            >
              <Plus className="w-4 h-4 mr-2" />
              New Insight
            </Button>
          </div>
          {insights.length > 0 && (
            <div className="flex items-center gap-6 mt-4 text-sm text-gray-400">
              <span className="flex items-center gap-2">
                <Sparkles className="w-4 h-4" />
                {insights.length} Total Insights
              </span>
              <span className="flex items-center gap-2">
                <Eye className="w-4 h-4" />
                {insights.reduce((sum, i) => sum + (i.viewCount || 0), 0)} Total Views
              </span>
            </div>
          )}
        </motion.div>

        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center justify-center py-12">
            <ColourfulLoader />
          </div>
        )}

        {/* Grouped Insights */}
        {!isLoading && Object.keys(groupedInsights).length > 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-12 space-y-4"
          >
            {Object.entries(groupedInsights).map(([type, group], index) => {
              const config = insightTypeConfig[type as InsightType];
              const Icon = config.icon;
              const isExpanded = expandedGroups.has(type);
              const hasHistory = group.history.length > 0;

              return (
                <motion.div
                  key={type}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  {/* Main Group Card */}
                  <Card
                    className={cn(
                      `${config.bgColor} ${config.borderColor} border backdrop-blur-sm transition-all duration-300`,
                      hasHistory ? 'cursor-pointer hover:shadow-lg' : ''
                    )}
                  >
                    <CardContent className="p-0">
                      {/* Latest Insight */}
                      <div className="p-6" onClick={() => handleViewInsight(group.latest.id)}>
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-4 flex-1">
                            <div
                              className={`p-3 rounded-lg bg-gradient-to-br ${config.color} shadow-lg`}
                            >
                              <Icon className="w-6 h-6 text-white" />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-start justify-between mb-2">
                                <div>
                                  <h3 className="text-lg font-semibold text-white mb-1">
                                    {group.latest.title}
                                  </h3>
                                  <div className="flex items-center gap-3 mb-2">
                                    {getStatusBadge(group.latest.status)}
                                    <span className="text-xs text-gray-400">
                                      {formatTimeAgo(group.latest.createdAt)}
                                    </span>
                                  </div>
                                </div>
                              </div>
                              <p className="text-gray-300 text-sm mb-3 line-clamp-2">
                                {group.latest.summary}
                              </p>
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-4 text-xs text-gray-400">
                                  <span className="flex items-center">
                                    <Calendar className="w-3 h-3 mr-1" />
                                    {new Date(group.latest.createdAt).toLocaleDateString()}
                                  </span>
                                  <span className="flex items-center">
                                    <Eye className="w-3 h-3 mr-1" />
                                    {group.latest.viewCount} views
                                  </span>
                                  {hasHistory && (
                                    <span className="flex items-center">
                                      <Clock className="w-3 h-3 mr-1" />
                                      {group.history.length} previous versions
                                    </span>
                                  )}
                                </div>
                                {hasHistory && (
                                  <button
                                    onClick={e => {
                                      e.stopPropagation();
                                      toggleGroup(type);
                                    }}
                                    className="p-1 hover:bg-white/10 rounded transition-colors"
                                  >
                                    {isExpanded ? (
                                      <ChevronDown className="w-4 h-4 text-gray-400" />
                                    ) : (
                                      <ChevronRight className="w-4 h-4 text-gray-400" />
                                    )}
                                  </button>
                                )}
                              </div>
                            </div>
                          </div>
                          <ArrowRight className="w-5 h-5 text-gray-400 ml-4" />
                        </div>
                      </div>

                      {/* History Section */}
                      <AnimatePresence>
                        {isExpanded && hasHistory && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: 'auto', opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.3 }}
                            className="border-t border-white/10 overflow-hidden"
                          >
                            <div className="p-4 bg-black/20">
                              <h4 className="text-sm font-medium text-gray-400 mb-3 flex items-center">
                                <Archive className="w-4 h-4 mr-2" />
                                Previous Versions
                              </h4>
                              <div className="space-y-3">
                                {group.history.map(insight => (
                                  <div
                                    key={insight.id}
                                    onClick={() => handleViewInsight(insight.id)}
                                    className="p-4 rounded-lg bg-white/5 hover:bg-white/10 transition-colors cursor-pointer"
                                  >
                                    <div className="flex items-start justify-between">
                                      <div className="flex-1">
                                        <div className="flex items-center gap-3 mb-1">
                                          <span className="text-sm text-white">
                                            {insight.title}
                                          </span>
                                          {getStatusBadge(insight.status)}
                                        </div>
                                        <p className="text-xs text-gray-400 line-clamp-1 mb-2">
                                          {insight.summary}
                                        </p>
                                        <div className="flex items-center space-x-3 text-xs text-gray-500">
                                          <span>{formatTimeAgo(insight.createdAt)}</span>
                                          <span>•</span>
                                          <span>{insight.viewCount} views</span>
                                        </div>
                                      </div>
                                      <ChevronRight className="w-4 h-4 text-gray-500 ml-4" />
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </motion.div>
        )}

        {/* Empty State / Create New Section */}
        {!isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <h2 className="text-xl font-semibold text-white mb-4">
              {insights.length > 0 ? 'Create New Insight' : 'Get Started'}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Object.entries(insightTypeConfig).map(([type, config], index) => {
                const Icon = config.icon;
                const hasExisting = groupedInsights[type];

                return (
                  <motion.div
                    key={type}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Card
                      className={cn(
                        `${config.bgColor} ${config.borderColor} border backdrop-blur-sm cursor-pointer hover:shadow-xl transition-all duration-300 h-full relative`,
                        hasExisting && 'ring-2 ring-white/20'
                      )}
                      onClick={() => handleCreateInsight(type as InsightType)}
                    >
                      {hasExisting && (
                        <div className="absolute top-3 right-3">
                          <Badge
                            variant="secondary"
                            className="text-xs bg-white/10 text-white border-white/20"
                          >
                            <TrendingUp className="w-3 h-3 mr-1" />
                            Update Available
                          </Badge>
                        </div>
                      )}
                      <CardHeader>
                        <div
                          className={`p-3 rounded-lg bg-gradient-to-br ${config.color} shadow-lg w-fit mb-4`}
                        >
                          <Icon className="w-8 h-8 text-white" />
                        </div>
                        <CardTitle className="text-white">
                          {type
                            .replace(/_/g, ' ')
                            .toLowerCase()
                            .replace(/\b\w/g, l => l.toUpperCase())}
                        </CardTitle>
                        <CardDescription className="text-gray-300">
                          {config.description}
                        </CardDescription>
                        {hasExisting && (
                          <div className="mt-3 text-xs text-gray-400">
                            Last updated: {formatTimeAgo(groupedInsights[type].latest.createdAt)}
                          </div>
                        )}
                      </CardHeader>
                    </Card>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>
        )}
      </div>
    </SimpleJobSeekerLayout>
  );
}
