import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Job } from './entities/job.entity';
import { JobAiHelpers } from './job.ai.helpers';

// Mock all dependencies directly
const mockJobRepository = {
  save: jest.fn(),
  findOne: jest.fn(),
};

const mockCompanyService = {
  updateByClientId: jest.fn(),
};

const mockOpenAiService = {
  generateJobTLDR: jest.fn(),
  generateSocialMediaDescription: jest.fn(),
};

const mockMultiAIContentService = {
  generateSocialMediaDescription: jest.fn(),
};

const mockContentGeneratorService = {
  generateCompanySummary: jest.fn(),
};

const mockJobCrudUtils = {
  findOne: jest.fn(),
};

// Mock OpenAI
jest.mock('openai', () => ({
  OpenAI: jest.fn().mockImplementation(() => ({
    chat: {
      completions: {
        create: jest.fn(),
      },
    },
  })),
}));

describe('JobAiHelpers Basic Tests', () => {
  let service: JobAiHelpers;

  beforeEach(async () => {
    jest.clearAllMocks();
    
    // Create a simple mock service instance
    service = new JobAiHelpers(
      mockJobRepository as any,
      mockCompanyService as any,
      mockOpenAiService as any,
      mockMultiAIContentService as any,
      mockContentGeneratorService as any,
      mockJobCrudUtils as any,
    );

    // Override the openai property
    (service as any).openai = {
      chat: {
        completions: {
          create: jest.fn(),
        },
      },
    };
  });

  describe('generateResponsibilities', () => {
    it('should generate and save job responsibilities', async () => {
      const mockJob = {
        id: 'job-123',
        jobType: 'Senior Software Engineer',
        department: 'Engineering',
        skills: ['TypeScript', 'React', 'Node.js'],
      };

      const mockResponsibilities = [
        '1. Design and implement scalable backend services',
        '2. Lead technical architecture decisions',
        '3. Mentor junior developers',
      ];

      mockJobCrudUtils.findOne.mockResolvedValue(mockJob);
      (service as any).openai.chat.completions.create.mockResolvedValue({
        choices: [{
          message: {
            content: mockResponsibilities.join('\n'),
          },
        }],
      });
      mockJobRepository.save.mockResolvedValue({
        ...mockJob,
        jobResponsibilities: mockResponsibilities,
      });

      const result = await service.generateResponsibilities('job-123');

      expect(mockJobCrudUtils.findOne).toHaveBeenCalledWith('job-123');
      expect((service as any).openai.chat.completions.create).toHaveBeenCalled();
      expect(mockJobRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          jobResponsibilities: mockResponsibilities,
        })
      );
      expect(result).toEqual(mockResponsibilities);
    });

    it('should handle empty skills array', async () => {
      const mockJob = {
        id: 'job-123',
        jobType: 'Manager',
        skills: [],
      };

      mockJobCrudUtils.findOne.mockResolvedValue(mockJob);
      (service as any).openai.chat.completions.create.mockResolvedValue({
        choices: [{
          message: {
            content: '1. General management responsibility',
          },
        }],
      });
      mockJobRepository.save.mockResolvedValue({
        ...mockJob,
        jobResponsibilities: ['1. General management responsibility'],
      });

      const result = await service.generateResponsibilities('job-123');

      expect(result).toEqual(['1. General management responsibility']);
    });
  });

  describe('generateSocialMediaDescription', () => {
    it('should generate social media description', async () => {
      const mockJob = {
        id: 'job-123',
        jobType: 'Software Engineer',
      };
      const mockDescription = '🚀 Join our team as a Software Engineer!';

      mockJobRepository.findOne.mockResolvedValue(mockJob);
      mockMultiAIContentService.generateSocialMediaDescription.mockResolvedValue(mockDescription);
      mockJobRepository.save.mockResolvedValue({
        ...mockJob,
        socialMediaDescription: mockDescription,
      });

      const result = await service.generateSocialMediaDescription('job-123');

      expect(mockJobRepository.findOne).toHaveBeenCalledWith({ where: { id: 'job-123' } });
      expect(mockMultiAIContentService.generateSocialMediaDescription).toHaveBeenCalledWith(mockJob);
      expect(result).toBe(mockDescription);
    });

    it('should throw error when job not found', async () => {
      mockJobRepository.findOne.mockResolvedValue(null);

      await expect(service.generateSocialMediaDescription('non-existent'))
        .rejects.toThrow('Job not found');
    });
  });

  describe('generateSummaryForCompany', () => {
    it('should generate company summary', async () => {
      const mockSummary = 'A leading tech company...';
      
      mockContentGeneratorService.generateCompanySummary.mockResolvedValue(mockSummary);
      mockCompanyService.updateByClientId.mockResolvedValue({});

      const result = await service.generateSummaryForCompany({
        url: 'https://example.com',
        clientId: 'client-123',
      });

      expect(mockContentGeneratorService.generateCompanySummary).toHaveBeenCalledWith('https://example.com');
      expect(mockCompanyService.updateByClientId).toHaveBeenCalledWith('client-123', {
        description: mockSummary,
      });
      expect(result).toEqual({
        success: true,
        summary: mockSummary,
      });
    });

    it('should handle null summary', async () => {
      mockContentGeneratorService.generateCompanySummary.mockResolvedValue(null);
      mockCompanyService.updateByClientId.mockResolvedValue({});

      const result = await service.generateSummaryForCompany({
        url: 'https://example.com',
        clientId: 'client-123',
      });

      expect(result.summary).toBeNull();
      expect(mockCompanyService.updateByClientId).toHaveBeenCalledWith('client-123', {
        description: '',
      });
    });
  });
});