import { Column, <PERSON><PERSON>ty, Index, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';

import {
  AvatarGender,
  VideoJDStatus,
  VideoJDType,
  VirtualBackgroundType,
} from '@/shared/types/video.types';
import { Job } from '@modules/entities';
import { BaseEntity } from '@shared/entities/base.entity';

@Entity('videoJDs')
@Index('idx_video_jds_job_id', ['jobId'])
@Index('idx_video_jds_status', ['status'])
@Index('idx_video_jds_client_id', ['clientId'])
@Index('idx_video_jds_virtual_background_type', ['virtualBackgroundType'])
export class VideoJD extends BaseEntity {
  @Column({ type: 'uuid' })
  jobId!: string;

  @Column({ type: 'varchar', nullable: true })
  gender!: AvatarGender;

  @Column({ type: 'varchar', nullable: true })
  languageCode!: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  voiceId!: string;

  @Column({ type: 'varchar', nullable: true })
  tone?: string;

  @Column({ type: 'varchar', nullable: true, default: 'medium' })
  scriptLength?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  avatarId!: string;

  @Column({ type: 'float', nullable: true })
  voiceSpeed!: number;

  @Column({ type: 'float', nullable: true })
  voicePitch!: number;

  @Column({ type: 'text', nullable: true })
  generatedScript!: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  synthesiaVideoId!: string;

  @Column({ type: 'enum', enum: VideoJDStatus, default: VideoJDStatus.SCRIPT_GENERATED })
  status!: VideoJDStatus;

  @Column({ type: 'text', nullable: true })
  videoUrl!: string;

  @ManyToOne(() => Job, (job) => job.videoJDs, {
    lazy: true,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'jobId' })
  job?: Promise<Job>;

  @Column({ nullable: true })
  jobType?: string;

  @Column({ type: 'varchar', nullable: true })
  clientId?: string;

  // New fields for live recording with virtual backgrounds
  @Column({
    type: 'enum',
    enum: VideoJDType,
    default: VideoJDType.AI_AVATAR,
  })
  type!: VideoJDType;

  @Column({
    type: 'enum',
    enum: VirtualBackgroundType,
    default: VirtualBackgroundType.NONE,
    nullable: true,
  })
  virtualBackgroundType?: VirtualBackgroundType;

  @Column({ type: 'text', nullable: true })
  virtualBackgroundImageUrl?: string;

  @Column({ type: 'int', nullable: true })
  recordingDuration?: number;

  @Column({ type: 'jsonb', nullable: true })
  recordingSettings?: {
    videoQuality?: string;
    audioQuality?: string;
    frameRate?: number;
    resolution?: string;
  };
}
