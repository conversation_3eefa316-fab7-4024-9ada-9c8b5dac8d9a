import { useEffect, useState } from 'react';

import {
  Activity,
  Briefcase,
  Calendar,
  Clock,
  Copy,
  EyeOff,
  Globe,
  Rocket,
  Users,
} from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';

import { IJob } from '@/entities/interfaces';
import { useMediaQuery } from '@/hooks/useMediaQuery';
import {
  jobDataArrayToIJobArray,
  useJobsStore,
  useJobStateStore,
  useJobStore,
} from '@/stores/unifiedJobStore';

import { Settings, Trash2, UserCheck } from 'lucide-react';
import { NavigationButton } from '../common/NavigationButton';
import GenericTable, { GenericTableProps } from '../Layouts/GenericTable';
import { showToast } from '../Toaster';
import CustomTooltip from '../ui/custom-tooltip';
import MobileJobCard from './MobileJobCard';

// Base classes with theme-aware hover effects - updated for better dark mode contrast and consistent colors
const baseClasses =
  'group relative flex items-center p-2 rounded-full transition-all duration-300 transform hover:scale-105 bg-[var(--button-secondary-bg)] border border-[var(--card-border)] shadow-sm backdrop-blur-[1px] dark:shadow-black/10 dark:hover:shadow-black/20';

interface PostedJobListProps {
  jobs: IJob[];
  onJobClick?: (job: IJob) => void;
  paginationData?: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
  onPageChange?: (page: number) => void;
  isLoading?: boolean;
}

const PostedJobsList = ({
  jobs: initialJobs = [],
  onJobClick,
  paginationData,
  onPageChange,
  isLoading = false,
}: PostedJobListProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { publishJob, unpublishJob } = useJobStore();

  // Create a local state for jobs to allow forcing re-renders
  const [jobs, setJobs] = useState<IJob[]>(initialJobs);
  const deleteJob = useJobsStore(state => state.deleteJob);
  const fetchJobsByStatus = useJobsStore(state => state.fetchJobsByStatus);

  // Update local jobs state when initialJobs changes
  useEffect(() => {
    setJobs(initialJobs);
  }, [initialJobs]);

  // Check if we're on mobile
  const isMobile = useMediaQuery('(max-width: 768px)');

  // Subscribe to job state changes from Zustand store
  useEffect(() => {
    // Subscribe to job state store changes
    const unsubscribe = useJobStateStore.subscribe(
      state => state.jobs,
      jobs => {
        // Update local jobs list when store changes
        // Always update, even if jobs is empty (to handle deletion of last job)
        if (Array.isArray(jobs)) {
          setJobs(jobDataArrayToIJobArray(jobs));
        }
      }
    );

    return unsubscribe;
  }, [setJobs]);

  const handleRowClick = (job: IJob) => {
    // Navigate to job management page
    router.push(`/jobs/${job.id}/manage`);

    // Call the parent's onJobClick if provided
    if (onJobClick) {
      onJobClick(job);
    }
  };

  const columns = [
    {
      key: 'isPublished',
      label: 'Status',
      icon: Globe,
      render: (_: any, job: IJob) => {
        const isPublished = (job as any).isPublished || false;

        return (
          <div className="flex justify-center">
            <div
              className="inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full text-xs font-medium backdrop-blur-md"
              style={{
                background: isPublished
                  ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(22, 163, 74, 0.15) 50%, rgba(34, 197, 94, 0.1) 100%)'
                  : 'linear-gradient(135deg, rgba(251, 191, 36, 0.1) 0%, rgba(245, 158, 11, 0.15) 50%, rgba(251, 191, 36, 0.1) 100%)',
                backdropFilter: 'blur(10px)',
                WebkitBackdropFilter: 'blur(10px)',
              }}
            >
              {isPublished ? (
                <>
                  <Rocket className="h-3.5 w-3.5 text-green-400 dark:text-green-300" />
                  <span className="text-green-500 dark:text-green-200 font-medium">Live</span>
                </>
              ) : (
                <>
                  <Clock className="h-3.5 w-3.5 text-amber-400 dark:text-amber-300" />
                  <span className="text-amber-500 dark:text-amber-200 font-medium">Draft</span>
                </>
              )}
            </div>
          </div>
        );
      },
    },
    {
      key: 'jobType',
      label: 'Position',
      icon: Briefcase,
      clickable: true,
      onClick: (job: IJob) => router.push(`/jobs/${job.id}/manage`),
      render: (value: string) => {
        const truncated = value && value.length > 20 ? `${value.substring(0, 20)}...` : value;
        return (
          <div className="group relative">
            <span className="text-sm">{truncated || 'N/A'}</span>
            {value && value.length > 20 && (
              <div className="absolute bottom-full left-0 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                {value}
              </div>
            )}
          </div>
        );
      },
    },

    {
      key: 'department',
      label: 'Department',
      icon: Activity,
      clickable: true,
      onClick: (job: IJob) => router.push(`/jobs/${job.id}/manage`),
      render: (value: string) => {
        const truncated = value && value.length > 20 ? `${value.substring(0, 20)}...` : value;
        return (
          <div className="group relative">
            <span className="text-sm">{truncated || 'N/A'}</span>
            {value && value.length > 20 && (
              <div className="absolute bottom-full left-0 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                {value}
              </div>
            )}
          </div>
        );
      },
    },

    {
      key: 'candidateStats',
      label: 'Candidates',
      icon: Users,
      render: (_: any, job: IJob) => {
        // Use totalCandidates directly from job object first, then fallback to candidateStats
        const totalCandidates = job.totalCandidates ?? job['candidateStats']?.totalCandidates ?? 0;
        return (
          <div className="text-center">
            <span className="text-sm">{totalCandidates}</span>
          </div>
        );
      },
    },
    {
      key: 'updatedAt',
      label: 'Updated',
      icon: Calendar,
      render: (date: string) =>
        new Date(date).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
        }),
    },
    {
      key: 'actions',
      label: 'Actions',
      icon: null,
      render: (_: any, job: IJob) => {
        // Use totalCandidates directly from job object first, then fallback to candidateStats
        const totalCandidates = job.totalCandidates ?? job['candidateStats']?.totalCandidates ?? 0;
        const hasCandidates = totalCandidates > 0;
        const isPublished = (job as any).isPublished || false;

        const handleManageClick = () => {
          router.push(`/jobs/${job.id}/manage`);
        };

        const handleCandidateScoresClick = () => {
          if (hasCandidates) {
            router.push(`/jobs/${job.id}/candidates`);
          } else {
            router.push(`/jobs/${job.id}/edit`);
          }
        };

        const handleDelete = async (e: React.MouseEvent) => {
          e.stopPropagation();
          if (window.confirm('Are you sure you want to delete this job?')) {
            try {
              await deleteJob(job.id);
              showToast({
                message: 'Job deleted successfully',
                isSuccess: true,
              });
            } catch (error) {
              console.error('Error deleting job:', error);
              showToast({
                message: 'Failed to delete job',
                isSuccess: false,
              });
            }
          }
        };

        const handleCopyUrl = async (e: React.MouseEvent) => {
          e.stopPropagation();
          try {
            const jobUrl = `${window.location.origin}/open-jobs/${job.id}`;
            await navigator.clipboard.writeText(jobUrl);
            showToast({
              message: 'Job URL copied to clipboard!',
              isSuccess: true,
            });
          } catch (error) {
            console.error('Failed to copy URL:', error);
            showToast({
              message: 'Failed to copy URL',
              isSuccess: false,
            });
          }
        };

        const handlePublishToggle = async (e: React.MouseEvent) => {
          e.stopPropagation();
          try {
            if (isPublished) {
              await unpublishJob(job.id, ['jobboard']);
              showToast({
                message: 'Job unpublished successfully',
                isSuccess: true,
              });
            } else {
              await publishJob(job.id, ['jobboard']);
              showToast({
                message: 'Job published successfully',
                isSuccess: true,
              });
            }
            // Refresh the jobs list through the store
            // Get current status filter from URL if any
            const currentStatus = searchParams.get('status') || 'ALL';
            const currentPage = parseInt(searchParams.get('page') || '1', 10);

            // Fetch jobs with the current filters to update the view
            await fetchJobsByStatus(currentPage, currentStatus, true);
          } catch (error) {
            console.error('Error updating job status:', error);
            showToast({
              message: 'Failed to update job status',
              isSuccess: false,
            });
          }
        };

        return (
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <NavigationButton
                icon={UserCheck}
                onClick={handleCandidateScoresClick}
                className="!text-xs !py-2"
              >
                <span className="hidden sm:inline">
                  {hasCandidates ? 'Assessments' : 'Evaluate Criteria'}
                </span>
                <span className="sm:hidden">{hasCandidates ? 'Assess' : 'Criteria'}</span>
              </NavigationButton>

              <NavigationButton
                icon={Settings}
                onClick={handleManageClick}
                className="!text-xs !py-2"
              >
                Manage
              </NavigationButton>
            </div>

            <div className="flex items-center gap-2 ml-auto">
              <CustomTooltip
                content={
                  <div className="flex items-center gap-1.5">
                    <Copy className="h-3.5 w-3.5 text-blue-400" />
                    <span className="text-xs font-medium">Copy job URL</span>
                  </div>
                }
              >
                <button
                  onClick={handleCopyUrl}
                  className="group relative p-2 rounded-full transition-all duration-300 overflow-hidden"
                  style={{
                    background:
                      'linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, rgba(37, 99, 235, 0.05) 50%, rgba(59, 130, 246, 0.03) 100%)',
                    backdropFilter: 'blur(8px)',
                    WebkitBackdropFilter: 'blur(8px)',
                    border: 'none',
                  }}
                >
                  <div
                    className="absolute inset-0 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    style={{
                      background:
                        'radial-gradient(circle at center, rgba(59, 130, 246, 0.2) 0%, transparent 70%)',
                    }}
                  />
                  <Copy className="h-4 w-4 text-white/40 group-hover:text-white/80 transition-colors duration-300 relative z-10" />
                </button>
              </CustomTooltip>

              <CustomTooltip
                content={
                  <div className="flex items-center gap-1.5">
                    {isPublished ? (
                      <>
                        <EyeOff className="h-3.5 w-3.5 text-gray-400" />
                        <span className="text-xs font-medium">Unpublish job</span>
                      </>
                    ) : (
                      <>
                        <Rocket className="h-3.5 w-3.5 text-green-400" />
                        <span className="text-xs font-medium">Publish to our job board</span>
                      </>
                    )}
                  </div>
                }
              >
                <button
                  onClick={handlePublishToggle}
                  className={`group relative p-2 rounded-full transition-all duration-300 overflow-hidden ${
                    isPublished
                      ? 'bg-gray-500/40 hover:bg-gray-600'
                      : 'bg-green-500/40 hover:bg-green-600'
                  }`}
                >
                  {isPublished ? (
                    <EyeOff className="h-4 w-4 text-white transition-colors duration-300 relative z-10" />
                  ) : (
                    <Rocket className="h-4 w-4 text-white transition-colors duration-300 relative z-10" />
                  )}
                </button>
              </CustomTooltip>

              <CustomTooltip
                content={
                  <div className="flex items-center gap-1.5">
                    <Trash2 className="h-3.5 w-3.5 text-red-400" />
                    <span className="text-xs font-medium">Delete job</span>
                  </div>
                }
              >
                <button
                  onClick={handleDelete}
                  className="group relative p-2 rounded-full transition-all duration-300 overflow-hidden bg-red-500/40 hover:bg-red-600"
                >
                  <Trash2 className="h-4 w-4 text-white transition-colors duration-300 relative z-10" />
                </button>
              </CustomTooltip>
            </div>
          </div>
        );
      },
    },
  ];

  const tableProps: GenericTableProps<IJob> = {
    data: jobs,
    columns,
    onRowClick: handleRowClick,
    disablePagination: false,
    paginationData,
    onPageChange,
    showPagination: true,
    isLoading,
    emptyStateConfig: {
      type: 'job',
      title: 'No Jobs Found',
      description:
        'Ready to build your dream team? Start by creating your first job description and let AI help you find the perfect candidates.',
      actionLabel: 'Create Job Description',
      actionRoute: '/job-description-creation',
      showButton: true,
      onAction: () => router.push('/job-description-creation'),
    },
  };

  // Render mobile job cards
  const renderMobileJobCards = () => {
    return (
      <div className="space-y-3 px-2">
        {jobs.map(job => (
          <MobileJobCard key={job.id} job={job} onJobClick={handleRowClick} />
        ))}
      </div>
    );
  };

  return (
    <>
      {isMobile ? (
        renderMobileJobCards()
      ) : (
        <div className="m-0 p-0">
          <GenericTable {...tableProps} />
        </div>
      )}
    </>
  );
};

export default PostedJobsList;
