import { ICandidate } from '@/entities/interfaces';
import { useJobStore } from '@/stores/unifiedJobStore';
import { useMatchRankDetailsStore } from '@/stores/matchrankDetailsStore';
import { AnimatePresence, motion } from 'framer-motion';
import { ArrowLeft, ChevronRight, Medal, Star, Target, Trophy, Users } from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { CandidateListItem } from '../CandidateListItem';
import { UnifiedCandidateView } from '../UnifiedCandidateView';
import Image from 'next/image';

interface GroupedCandidates {
  topTier: ICandidate[];
  secondTier: ICandidate[];
  others: ICandidate[];
  unranked: ICandidate[];
  shortlisted: ICandidate[];
}

interface SimplifiedCandidatesComposition2Props {
  candidates: ICandidate[] | GroupedCandidates;
  currentPage: number;
  onPageChange: (page: number) => void;
  isAtsJob?: boolean;
  jobId?: string;
  fetchCandidateById?: (jobId: string, candidateId: string) => Promise<ICandidate | null>;
}

// Helper to get all candidates from grouped data
const getAllCandidates = (groupedCandidates: GroupedCandidates): ICandidate[] => {
  return [
    ...groupedCandidates.shortlisted,
    ...groupedCandidates.topTier,
    ...groupedCandidates.secondTier,
    ...groupedCandidates.others,
    ...(groupedCandidates.unranked || []),
  ];
};

// Helper to get the highest ranking candidate
const getHighestRankingCandidate = (groupedCandidates: GroupedCandidates): ICandidate | null => {
  if (groupedCandidates.shortlisted?.length > 0) return groupedCandidates.shortlisted[0];
  if (groupedCandidates.topTier?.length > 0) return groupedCandidates.topTier[0];
  if (groupedCandidates.secondTier?.length > 0) return groupedCandidates.secondTier[0];
  if (groupedCandidates.others?.length > 0) return groupedCandidates.others[0];
  if (groupedCandidates.unranked?.length > 0) return groupedCandidates.unranked[0];
  return null;
};

const SimplifiedCandidatesComposition2Component: React.FC<
  SimplifiedCandidatesComposition2Props
> = ({ candidates, currentPage, onPageChange, isAtsJob = false, jobId, fetchCandidateById }) => {
  const { selectedCandidate, setSelectedCandidate } = useMatchRankDetailsStore();
  const { currentJob, fetchCandidates, stats } = useJobStore();
  const [expandedSection, setExpandedSection] = useState<string | null>('shortlisted');
  const autoSelectedRef = useRef(false);
  const router = useRouter();

  // Determine if candidates is already grouped
  const isGroupedData = candidates && typeof candidates === 'object' && 'topTier' in candidates;
  const groupedCandidates = isGroupedData
    ? (candidates as GroupedCandidates)
    : {
        shortlisted: [],
        topTier: [],
        secondTier: [],
        others: [],
        unranked: candidates as ICandidate[],
      };

  // Get all candidates as a flat array
  const allCandidates = useMemo(() => getAllCandidates(groupedCandidates), [groupedCandidates]);

  // Enhanced candidate selection with optimistic updates
  const handleCandidateSelect = useCallback(
    async (candidate: ICandidate) => {
      try {
        setSelectedCandidate(null);
        await new Promise(resolve => setTimeout(resolve, 50));
        setSelectedCandidate(candidate);

        if (jobId && candidate.id && fetchCandidateById) {
          const fullCandidate = await fetchCandidateById(jobId, candidate.id);
          if (fullCandidate) {
            setSelectedCandidate(fullCandidate);
          }
        }
      } catch (error) {
        console.error('Error fetching candidate details:', error);
      }
    },
    [fetchCandidateById, jobId, setSelectedCandidate]
  );

  // Handle candidate status changes
  const handleCandidateStatusChange = useCallback(async () => {
    if (!jobId) return;
    try {
      await fetchCandidates(jobId);
    } catch (error) {
      console.error('Error updating candidate status:', error);
    }
  }, [jobId, fetchCandidates]);

  // Auto-select first candidate
  useEffect(() => {
    if (allCandidates.length > 0) {
      if (!selectedCandidate && !autoSelectedRef.current) {
        const highestRankingCandidate = getHighestRankingCandidate(groupedCandidates);
        if (highestRankingCandidate) {
          autoSelectedRef.current = true;
          setSelectedCandidate(highestRankingCandidate);
        }
      } else if (selectedCandidate) {
        const candidateExists = allCandidates.some(c => c.id === selectedCandidate.id);
        if (!candidateExists) {
          const highestRankingCandidate = getHighestRankingCandidate(groupedCandidates);
          if (highestRankingCandidate) {
            setSelectedCandidate(highestRankingCandidate);
          } else {
            setSelectedCandidate(null);
          }
        }
        autoSelectedRef.current = false;
      }
    } else if (selectedCandidate) {
      setSelectedCandidate(null);
      autoSelectedRef.current = false;
    }
  }, [allCandidates, selectedCandidate?.id, groupedCandidates, setSelectedCandidate]);

  // Get threshold values - handle string values from database
  const topCandidateThreshold = currentJob?.topCandidateThreshold
    ? parseFloat(currentJob.topCandidateThreshold.toString())
    : 80;
  const secondTierCandidateThreshold = currentJob?.secondTierCandidateThreshold
    ? parseFloat(currentJob.secondTierCandidateThreshold.toString())
    : 60;

  const toggleSection = (section: string) => {
    setExpandedSection(prev => (prev === section ? null : section));
  };

  if (!selectedCandidate) {
    return (
      <div className="flex items-center justify-center h-full text-foreground">
        <p>No candidates available</p>
      </div>
    );
  }

  return (
    <div className="h-screen w-screen flex">
      {/* Left Sidebar - Candidate List */}
      <div className="w-80 flex-shrink-0 border-r border-gray-300/10 bg-gray-900/10 flex flex-col">
        <div>
          {/* Back to Jobs Button with bottom border - matching header height */}
          <div className="px-6 py-6 border-b border-gray-300/10 flex items-center h-[80px]">
            <button
              onClick={() => router.push('/jobs')}
              className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
              <span>Back to Jobs</span>
            </button>
          </div>
        </div>

        {/* Candidate Rankings section - scrollable */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-4">
            <h2 className="text-base font-semibold mb-3 text-gray-300">Candidate Rankings</h2>

            {/* Shortlisted - Always show even if empty */}
            <CandidateSection
              title="Shortlisted"
              candidates={groupedCandidates.shortlisted}
              icon={<Star className="w-3.5 h-3.5 text-yellow-500/80 fill-yellow-500/80" />}
              isExpanded={expandedSection === 'shortlisted'}
              onToggle={() => toggleSection('shortlisted')}
              selectedCandidateId={selectedCandidate?.id}
              onSelectCandidate={handleCandidateSelect}
            />

            {/* Top Candidates - Filter out shortlisted */}
            <CandidateSection
              title="Top Candidates"
              candidates={groupedCandidates.topTier.filter(c => c.status !== 'SHORTLISTED')}
              icon={<Trophy className="w-3.5 h-3.5 text-purple-400/80" />}
              isExpanded={expandedSection === 'top-tier'}
              onToggle={() => toggleSection('top-tier')}
              selectedCandidateId={selectedCandidate?.id}
              onSelectCandidate={handleCandidateSelect}
            />

            {/* Second Tier - Filter out shortlisted */}
            <CandidateSection
              title="Second Tier"
              candidates={groupedCandidates.secondTier.filter(c => c.status !== 'SHORTLISTED')}
              icon={<Medal className="w-3.5 h-3.5 text-gray-400/80" />}
              isExpanded={expandedSection === 'second-tier'}
              onToggle={() => toggleSection('second-tier')}
              selectedCandidateId={selectedCandidate?.id}
              onSelectCandidate={handleCandidateSelect}
            />

            {/* Other Candidates - Filter out shortlisted */}
            {groupedCandidates.others.filter(c => c.status !== 'SHORTLISTED').length > 0 && (
              <CandidateSection
                title="Other Candidates"
                candidates={groupedCandidates.others.filter(c => c.status !== 'SHORTLISTED')}
                icon={<Target className="w-3.5 h-3.5 text-gray-400/60" />}
                isExpanded={expandedSection === 'others'}
                onToggle={() => toggleSection('others')}
                selectedCandidateId={selectedCandidate?.id}
                onSelectCandidate={handleCandidateSelect}
              />
            )}

            {/* Unranked - Filter out shortlisted */}
            {groupedCandidates.unranked.filter(c => c.status !== 'SHORTLISTED').length > 0 && (
              <CandidateSection
                title="Unranked"
                candidates={groupedCandidates.unranked.filter(c => c.status !== 'SHORTLISTED')}
                icon={<Users className="w-3.5 h-3.5 text-gray-400/60" />}
                isExpanded={expandedSection === 'unranked'}
                onToggle={() => toggleSection('unranked')}
                selectedCandidateId={selectedCandidate?.id}
                onSelectCandidate={handleCandidateSelect}
              />
            )}
          </div>
        </div>

        {/* Kaleido Logo at bottom */}
        <div className="p-6">
          <Image
            src="/images/logos/kaleido-logo-only.webp"
            alt="Kaleido"
            width={56}
            height={56}
            className="transition-all"
          />
        </div>
      </div>

      {/* Right Content - Candidate Details */}
      <div className="flex-1 overflow-hidden">
        <UnifiedCandidateView
          candidate={selectedCandidate}
          jobId={jobId || ''}
          jobTitle={currentJob?.jobTitle || currentJob?.jobType}
          onCandidateSelect={handleCandidateSelect}
          onStatusUpdate={handleCandidateStatusChange}
          topCandidateThreshold={topCandidateThreshold}
          secondTierCandidateThreshold={secondTierCandidateThreshold}
          stats={stats}
        />
      </div>
    </div>
  );
};

// Candidate Section Component
interface CandidateSectionProps {
  title: string;
  candidates: ICandidate[];
  icon: React.ReactNode;
  isExpanded: boolean;
  onToggle: () => void;
  selectedCandidateId?: string;
  onSelectCandidate: (candidate: ICandidate) => void;
}

const CandidateSection: React.FC<CandidateSectionProps> = ({
  title,
  candidates,
  icon,
  isExpanded,
  onToggle,
  selectedCandidateId,
  onSelectCandidate,
}) => {
  return (
    <div className="mb-2">
      <button
        onClick={onToggle}
        className="w-full flex items-center justify-between p-2 hover:bg-white/[0.02] rounded-lg transition-all duration-200 group"
      >
        <div className="flex items-center gap-2.5">
          <div className="opacity-60 group-hover:opacity-80 transition-opacity">{icon}</div>
          <span className="font-medium text-sm text-gray-300 group-hover:text-gray-100 transition-colors">
            {title}
          </span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-[11px] font-medium text-gray-500 bg-gray-800/50 px-2 py-0.5 rounded-md">
            {candidates.length}
          </span>
          <ChevronRight
            className={`w-3.5 h-3.5 text-gray-500 transition-transform ${isExpanded ? 'rotate-90' : ''}`}
          />
        </div>
      </button>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="overflow-hidden"
          >
            <div className="pl-1 pr-1 py-1 space-y-0.5">
              {candidates.length > 0 ? (
                candidates.map(candidate => (
                  <CandidateItem
                    key={candidate.id}
                    candidate={candidate}
                    isSelected={selectedCandidateId === candidate.id}
                    onSelect={onSelectCandidate}
                  />
                ))
              ) : (
                <p className="text-sm text-gray-500 italic py-2">No candidates</p>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Candidate Item Component - Now using CandidateListItem
const CandidateItem: React.FC<{
  candidate: ICandidate;
  isSelected: boolean;
  onSelect: (candidate: ICandidate) => void;
}> = ({ candidate, isSelected, onSelect }) => {
  return (
    <CandidateListItem
      candidate={candidate}
      isSelected={isSelected}
      onSelect={onSelect}
      isShortlisted={candidate.status === 'SHORTLISTED'}
    />
  );
};

// Memoized component to prevent unnecessary re-renders
export const SimplifiedCandidatesComposition2 = React.memo(
  SimplifiedCandidatesComposition2Component,
  (prevProps, nextProps) => {
    return (
      prevProps.currentPage === nextProps.currentPage &&
      prevProps.isAtsJob === nextProps.isAtsJob &&
      prevProps.jobId === nextProps.jobId &&
      JSON.stringify(prevProps.candidates) === JSON.stringify(nextProps.candidates)
    );
  }
);
