import { useEffect, useState } from 'react';

import { useJobsStore } from '@/stores/unifiedJobStore';

import useEnhancedUserData from './useEnhancedUserData';

export interface JobStats {
  totalJobs: number;
  jobsByStatus: Record<string, number>;
  jobsWithVideoJDs: number;
  jobsWithCandidates: number;
  departmentDistribution: { name: string; value: number }[];
  timeFrame: 'daily' | 'weekly' | 'monthly' | 'yearly';
}

// Backend response interface based on actual API response
export interface BackendJobStats {
  jobs: {
    total: number;
    statusBreakdown: Record<string, number>;
    recentJobs: any[];
    newJobs: Record<string, number>;
    activeJobs: Record<string, number>;
    closedJobs: Record<string, number>;
    jobTrends: any[];
  };
  videoJDs: {
    total: number;
    completed: number;
    pending: number;
  };
  candidates: {
    total: number;
    matched: number;
  };
  metrics: {
    totalJobs: number;
    totalCandidates: number;
  };
  userRole: string;
}

interface UseJobStatsProps {
  timeFrame?: 'daily' | 'weekly' | 'monthly' | 'yearly';
}

// This interface is defined in the jobsStore

export function useJobStats({ timeFrame = 'weekly' }: UseJobStatsProps = {}) {
  // Use the centralized enhanced user data
  const { userData, isLoading: isLoadingEnhanced, error, refetch } = useEnhancedUserData();

  // Use the existing jobsStore to access jobs data as fallback
  const jobsByStatus = useJobsStore(state => state.jobsByStatus);
  const isLoadingJobsStore = useJobsStore(state => state.isLoading);
  const fetchJobsByStatus = useJobsStore(state => state.fetchJobsByStatus);
  const [isClient, setIsClient] = useState(false);

  // First, detect if we're on the client
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Only fetch data from jobsStore on the client side as a fallback when enhanced data is not available
  useEffect(() => {
    if (isClient && !userData && !isLoadingEnhanced) {
      // Fetch jobs for ALL status with statistics included
      fetchJobsByStatus(1, 'ALL');
    }
  }, [fetchJobsByStatus, timeFrame, isClient, userData, isLoadingEnhanced]);

  // Get the statistics from the jobsStore as fallback
  const storeStats = jobsByStatus['ALL']?.stats;
  const isLoading = isLoadingEnhanced || isLoadingJobsStore || !isClient;

  // If we have enhanced user data with dashboard stats, use them directly
  if (userData?.dashboardStats) {
    return {
      jobStats: userData.dashboardStats as BackendJobStats,
      isLoading,
      error,
      refetch,
    };
  }

  // If we have stats from the jobsStore API, use them as fallback
  const jobStats: JobStats | undefined = storeStats
    ? {
        ...storeStats,
        timeFrame: timeFrame, // Override timeFrame with the one from props
      }
    : undefined;

  return {
    jobStats: jobStats || {
      totalJobs: 0,
      jobsByStatus: {},
      jobsWithVideoJDs: 0,
      jobsWithCandidates: 0,
      departmentDistribution: [],
      timeFrame,
    },
    isLoading,
    error,
    refetch: () => fetchJobsByStatus(1, 'ALL'),
  };
}
