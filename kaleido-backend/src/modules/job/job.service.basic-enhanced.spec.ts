import { Repository } from 'typeorm';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { NotFoundException } from '@nestjs/common';

import { CandidateStatus } from '@/shared/types';
import { JobStatus as JobStatusType } from '@/shared/types/job.types';
import { CandidateEvaluation } from '../candidate/entities/candidate-evaluation.entity';
import { Candidate } from '../candidate/entities/candidate.entity';
import { CompanyService } from '../company/company.service';
import { OpenaiService } from '../../shared/services/openai.service';
import { UpdateJobDto } from './dto/update-job.dto';
import { Job } from './entities/job.entity';
import { JobService } from './job.service';
import { JobApplication } from '../job-seeker/entities/job-application.entity';
import { VideoResponse } from '../video-response/entities/video-response.entity';
import { LinkedInService } from '../vendors/linkedin/linkedin.service';
import { JobMatchRankHelper } from './job.matchrank.helper';
import { JobAiHelpers } from './job.ai.helpers';
import { JobCrudUtils } from './job.crud.utils';
import { JobCandidatesHelpers } from './job.candidates.helpers';
import { EnhancedJobPublishingService } from '../company/services/enhanced-job-publishing.service';

describe('JobService Enhanced Tests - Complex Logic', () => {
  let service: JobService;
  let jobRepository: jest.Mocked<Repository<Job>>;
  let candidateRepository: jest.Mocked<Repository<Candidate>>;
  let candidateEvaluationRepository: jest.Mocked<Repository<CandidateEvaluation>>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        JobService,
        {
          provide: getRepositoryToken(Job),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
            update: jest.fn(),
            manager: {
              connection: {
                createQueryRunner: jest.fn(() => ({
                  startTransaction: jest.fn(),
                  commitTransaction: jest.fn(),
                  rollbackTransaction: jest.fn(),
                  release: jest.fn(),
                  query: jest.fn(),
                })),
              },
            },
          },
        },
        {
          provide: getRepositoryToken(Candidate),
          useValue: {
            update: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(CandidateEvaluation),
          useValue: {
            update: jest.fn(),
          },
        },
        // Minimal mocks for other dependencies
        { provide: getRepositoryToken(JobApplication), useValue: {} },
        { provide: getRepositoryToken(VideoResponse), useValue: {} },
        { provide: CompanyService, useValue: {} },
        { provide: OpenaiService, useValue: {} },
        { provide: LinkedInService, useValue: {} },
        { provide: JobMatchRankHelper, useValue: {} },
        { provide: JobAiHelpers, useValue: {} },
        { provide: JobCrudUtils, useValue: {} },
        { provide: JobCandidatesHelpers, useValue: {} },
        { provide: EnhancedJobPublishingService, useValue: {} },
      ],
    }).compile();

    service = module.get<JobService>(JobService);
    jobRepository = module.get(getRepositoryToken(Job));
    candidateRepository = module.get(getRepositoryToken(Candidate));
    candidateEvaluationRepository = module.get(getRepositoryToken(CandidateEvaluation));
  });

  describe('Critical Criteria Change Detection', () => {
    it('should detect when skills have changed and update candidate status', async () => {
      // Arrange
      const existingJob = {
        id: 'job-123',
        skills: ['TypeScript', 'React'],
        requirements: ['5+ years'],
        candidateEvaluations: [],
      };

      const updateDto: UpdateJobDto = {
        skills: ['Python', 'Django'], // Different skills
      };

      jobRepository.findOne.mockResolvedValue(existingJob as any);
      jobRepository.save.mockResolvedValue({ ...existingJob, ...updateDto } as any);
      candidateRepository.update.mockResolvedValue({ affected: 5 } as any);
      candidateEvaluationRepository.update.mockResolvedValue({ affected: 5 } as any);

      // Act
      await service.update('job-123', updateDto);

      // Assert - Candidates should be marked as MATCH_MODIFIED
      expect(candidateRepository.update).toHaveBeenCalledWith(
        { jobId: 'job-123', status: CandidateStatus.MATCHED },
        { status: CandidateStatus.MATCH_MODIFIED }
      );
      expect(candidateEvaluationRepository.update).toHaveBeenCalledWith(
        { jobId: 'job-123', status: CandidateStatus.MATCHED },
        { status: CandidateStatus.MATCH_MODIFIED }
      );
    });

    it('should NOT mark candidates as modified when only thresholds change', async () => {
      // Arrange
      const existingJob = {
        id: 'job-123',
        skills: ['TypeScript', 'React'],
        topCandidateThreshold: 80,
        secondTierCandidateThreshold: 60,
        candidateEvaluations: [],
      };

      const updateDto: any = {
        topCandidateThreshold: 85, // Only threshold changed
        secondTierCandidateThreshold: 65,
      };

      const queryRunner = {
        startTransaction: jest.fn(),
        commitTransaction: jest.fn(),
        rollbackTransaction: jest.fn(),
        release: jest.fn(),
        query: jest.fn().mockResolvedValue({}),
      };

      jobRepository.findOne.mockResolvedValue(existingJob as any);
      jobRepository.save.mockResolvedValue({ ...existingJob, ...updateDto } as any);
      (jobRepository.manager.connection.createQueryRunner as jest.Mock).mockReturnValue(queryRunner);

      // Act
      await service.update('job-123', updateDto);

      // Assert - Should update tiers, not statuses
      expect(candidateRepository.update).not.toHaveBeenCalledWith(
        expect.any(Object),
        { status: CandidateStatus.MATCH_MODIFIED }
      );
      expect(queryRunner.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE candidate_evaluations'),
        [85, 65, 'job-123']
      );
    });
  });

  describe('Threshold-based Tier Updates', () => {
    it('should update candidate tiers efficiently using batch SQL', async () => {
      // Arrange
      const existingJob = {
        id: 'job-123',
        topCandidateThreshold: 80,
        secondTierCandidateThreshold: 60,
        candidateEvaluations: [],
      };

      const updateDto: any = {
        topCandidateThreshold: 90,
        secondTierCandidateThreshold: 70,
      };

      const queryRunner = {
        startTransaction: jest.fn(),
        commitTransaction: jest.fn(),
        rollbackTransaction: jest.fn(),
        release: jest.fn(),
        query: jest.fn().mockResolvedValue({}),
      };

      jobRepository.findOne.mockResolvedValue(existingJob as any);
      jobRepository.save.mockResolvedValue({ ...existingJob, ...updateDto } as any);
      (jobRepository.manager.connection.createQueryRunner as jest.Mock).mockReturnValue(queryRunner);

      // Act
      await service.update('job-123', updateDto);

      // Assert
      expect(queryRunner.startTransaction).toHaveBeenCalled();
      expect(queryRunner.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE candidate_evaluations'),
        [90, 70, 'job-123']
      );
      expect(queryRunner.query).toHaveBeenCalledWith(
        expect.stringContaining('UPDATE candidates'),
        ['job-123']
      );
      expect(queryRunner.commitTransaction).toHaveBeenCalled();
    });

    it('should rollback transaction on error', async () => {
      // Arrange
      const existingJob = {
        id: 'job-123',
        topCandidateThreshold: 80,
        candidateEvaluations: [],
      };

      const updateDto: any = {
        topCandidateThreshold: 90,
      };

      const queryRunner = {
        startTransaction: jest.fn(),
        commitTransaction: jest.fn(),
        rollbackTransaction: jest.fn(),
        release: jest.fn(),
        query: jest.fn().mockRejectedValue(new Error('DB Error')),
      };

      jobRepository.findOne.mockResolvedValue(existingJob as any);
      jobRepository.save.mockResolvedValue({ ...existingJob, ...updateDto } as any);
      (jobRepository.manager.connection.createQueryRunner as jest.Mock).mockReturnValue(queryRunner);

      // Act & Assert
      await expect(service.update('job-123', updateDto)).rejects.toThrow('DB Error');
      expect(queryRunner.rollbackTransaction).toHaveBeenCalled();
      expect(queryRunner.release).toHaveBeenCalled();
    });
  });

  describe('Specialized Update Methods', () => {
    it('should update only culture fit fields without side effects', async () => {
      // Arrange
      const existingJob = { id: 'job-123' };
      const updateData = {
        cultureFitQuestions: [{ question: 'What motivates you?' }],
        cultureFitDescription: 'We value collaboration',
      };

      jobRepository.findOne.mockResolvedValue(existingJob as any);
      jobRepository.update.mockResolvedValue({ affected: 1 } as any);

      // Act
      await service.updateCultureFitOnly('job-123', updateData);

      // Assert
      expect(jobRepository.update).toHaveBeenCalledWith('job-123', updateData);
      expect(candidateRepository.update).not.toHaveBeenCalled();
    });

    it('should update only matchrank criteria without side effects', async () => {
      // Arrange
      const existingJob = { id: 'job-123' };
      const updateData = {
        topCandidateThreshold: 85,
        requirements: ['8+ years experience'],
      };

      jobRepository.findOne.mockResolvedValue(existingJob as any);
      jobRepository.update.mockResolvedValue({ affected: 1 } as any);

      // Act
      await service.updateMatchRankCriteriaOnly('job-123', updateData);

      // Assert
      expect(jobRepository.update).toHaveBeenCalledWith('job-123', updateData);
      expect(candidateRepository.update).not.toHaveBeenCalled();
      expect(candidateEvaluationRepository.update).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should throw NotFoundException when job not found during update', async () => {
      // Arrange
      jobRepository.findOne.mockResolvedValue(null);

      // Act & Assert
      await expect(
        service.update('non-existent', { jobType: 'New Title' })
      ).rejects.toThrow(NotFoundException);
    });
  });
});