import { ICandidate, IJob } from '@/entities/interfaces';

import { showToast } from '@/components/Toaster';
import { JobStatus } from '@/entities/Job.entities';
import apiHelper from '@/lib/apiHelper';
import { create } from 'zustand';
import { useMatchRankJobsStore } from './matchRankJobsStore';

type ViewMode = 'view' | 'edit' | 'ranked' | 'list';

interface MatchRankDetailsState {
  // Core state
  isOpen: boolean;
  selectedJobId: string | null;
  selectedJob: IJob | null;
  viewMode: ViewMode;
  candidateCount: number;
  isProcessing: boolean;
  selectedCandidate: ICandidate | null;
  showUploader: boolean;
  isUploadMode: boolean;
  hasFormChanges: boolean;
  hasFileUploads: boolean;

  // Credit calculation state
  lastRankedCandidateCount: number;
  jobDescriptionChanged: boolean;

  // Cache to prevent unnecessary API calls
  jobCache: Record<string, IJob>;
  lastJobDataRefreshTimestamps: Record<string, number>;
  lastJobsRefreshTimestamp: number;

  // Actions
  setIsOpen: (isOpen: boolean) => void;

  setSelectedJob: (job: IJob | null) => void;
  setViewMode: (mode: ViewMode) => void;
  updateCandidateCount: (count: number) => void;
  setIsProcessing: (processing: boolean) => void;
  setSelectedCandidate: (candidate: ICandidate | null) => void;
  setShowUploader: (show: boolean, isUploadMode?: boolean) => void;
  setHasFormChanges: (hasChanges: boolean) => void;
  setHasFileUploads: (hasUploads: boolean) => void;
  setJobDescriptionChanged: (changed: boolean) => void;
  setLastRankedCandidateCount: (count: number) => void;
  resetFormState: () => void;

  // Job handling
  getJobById: (jobId: string) => IJob | null;
  cacheJob: (job: IJob) => void;
  handleRanking: (jobId: string) => Promise<void>;
  refreshJobData: (
    jobId: string,
    forceRefresh?: boolean,
    mode?: 'edit' | 'view'
  ) => Promise<IJob | null>;
  refreshAfterScouting: (jobId: string) => Promise<void>;
  updateCandidateStatus: (
    candidateId: string,
    status: string,
    jobId: string,
    dateSensitiveInfo?: string
  ) => Promise<any>;
  updateCandidateData: (candidateId: string, updatedData: Partial<ICandidate>) => void;
  checkCulturalFitSetup: (response: any) => boolean;

  // UI handlers
  handleEditClick: () => void;
  handleUploaderClose: () => void;

  // Combined actions
  openMatchRankDetails: (job: IJob, mode?: ViewMode) => void;
  closeMatchRankDetails: () => void;

  // URL parameter utilities
  clearUrlParameter: (paramName: string, searchParams?: URLSearchParams) => string;
  clearUrlParameters: (paramNames: string[], searchParams?: URLSearchParams) => string;
}

export const useMatchRankDetailsStore = create<MatchRankDetailsState>((set, get) => ({
  // Initial state
  isOpen: false,
  selectedJobId: null,
  selectedJob: null,
  viewMode: 'view',
  jobCache: {},
  candidateCount: 0,
  isProcessing: false,
  selectedCandidate: null,
  showUploader: false,
  isUploadMode: false,
  hasFormChanges: false,
  hasFileUploads: false,
  lastRankedCandidateCount: 0,
  jobDescriptionChanged: false,
  lastJobDataRefreshTimestamps: {},
  lastJobsRefreshTimestamp: 0,

  // Basic actions
  setIsOpen: isOpen => set({ isOpen }),

  setSelectedJob: job => {
    if (job) {
      // Update candidate count when setting a job
      const candidateCount = job.candidates?.length || 0;
      const currentViewMode = get().viewMode;

      // Don't automatically change viewMode based on job status
      // Let the URL parameters and explicit setViewMode calls control the viewMode
      // Only set to edit mode if the current viewMode is the initial 'view' and job is NEW
      const shouldForceEditMode = job.status === 'NEW' && currentViewMode === 'view';
      const newViewMode = shouldForceEditMode ? 'edit' : currentViewMode;

      // When setting a job, also update the cache and set the viewMode if job is NEW
      set(state => ({
        selectedJob: job,
        selectedJobId: job.id,
        viewMode: newViewMode,
        candidateCount,
        // Initialize lastRankedCandidateCount if job is already matched/ranked
        lastRankedCandidateCount:
          job.status === 'MATCHED' ? candidateCount : state.lastRankedCandidateCount,
        jobCache: {
          ...state.jobCache,
          [job.id]: job,
        },
        // Also update lastJobDataRefreshTimestamps to prevent immediate API refresh
        lastJobDataRefreshTimestamps: {
          ...state.lastJobDataRefreshTimestamps,
          [job.id]: Date.now(),
        },
      }));
    } else {
      set({ selectedJob: null, selectedJobId: null });
    }
  },

  setViewMode: mode => set({ viewMode: mode }),

  updateCandidateCount: count => set({ candidateCount: count }),

  setIsProcessing: processing => set({ isProcessing: processing }),

  setSelectedCandidate: candidate => {
    // If clearing the selected candidate, just set to null
    if (!candidate) {
      set({ selectedCandidate: null });
      return;
    }

    // Get the current state
    const state = get();

    // If we have a selected job, verify the candidate belongs to it
    if (state.selectedJob && state.selectedJob.candidates) {
      const candidateBelongsToJob = state.selectedJob.candidates.some(c => c.id === candidate.id);

      // Only set the candidate if it belongs to the current job
      if (candidateBelongsToJob) {
        set({ selectedCandidate: candidate });
      }
    } else {
      // If no job is selected or job has no candidates, allow setting the candidate
      set({ selectedCandidate: candidate });
    }
  },

  setShowUploader: (show, isUploadMode = false) =>
    set({
      showUploader: show,
      isUploadMode,
    }),

  setHasFormChanges: hasFormChanges => set({ hasFormChanges }),

  setHasFileUploads: hasFileUploads => set({ hasFileUploads }),

  setJobDescriptionChanged: jobDescriptionChanged => set({ jobDescriptionChanged }),

  setLastRankedCandidateCount: lastRankedCandidateCount => set({ lastRankedCandidateCount }),

  resetFormState: () =>
    set({ hasFormChanges: false, hasFileUploads: false, jobDescriptionChanged: false }),

  // Job handling
  getJobById: jobId => {
    const state = get();

    // Check if it's the currently selected job
    if (state.selectedJob?.id === jobId) {
      return state.selectedJob;
    }

    // Otherwise check the cache
    return state.jobCache[jobId] || null;
  },

  cacheJob: job => {
    if (!job?.id) return;

    // Add job to cache without changing selected job
    set(state => ({
      jobCache: {
        ...state.jobCache,
        [job.id]: job,
      },
      // Also update lastJobDataRefreshTimestamps to prevent immediate API refresh
      lastJobDataRefreshTimestamps: {
        ...state.lastJobDataRefreshTimestamps,
        [job.id]: Date.now(),
      },
    }));
  },

  handleRanking: async jobId => {
    const { setIsProcessing, setShowUploader, setViewMode } = get();

    try {
      setIsProcessing(true);
      setShowUploader(true, false);

      // Get jobsStore reference - lazy import to avoid circular dependency
      const { useJobsStore } = await import('@/stores/unifiedJobStore');
      const jobsStore = useJobsStore.getState();

      // Create unique pending request key for this operation
      const matchRankRequestKey = `match_rank_${jobId}`;

      // Start the match and rank process
      if (!jobsStore.hasPendingRequest(matchRankRequestKey)) {
        const matchRankRequest = apiHelper.post(`/jobs/start-match-rank/${jobId}`, {
          // You can add options here if needed
          topTierThreshold: 0.7,
          secondTierThreshold: 0.5,
        });

        jobsStore.addPendingRequest(matchRankRequestKey, matchRankRequest);

        try {
          // Wait for the request to complete and get the response
          const response = await matchRankRequest;

          // Check if the response contains a queueJobId
          if (response && response.queueJobId) {
            // Add the job to the matchRankJobsStore with the queue job ID
            const { addJob } = useMatchRankJobsStore.getState();

            addJob({
              jobId: jobId, // Use the actual job ID, not the queue ID
              status: 'queued',
              message: 'Match and rank process started...',
              metadata: { queueJobId: response.queueJobId }, // Store queue ID in metadata
            });

            // Log the current state of the matchRankJobsStore for debugging
          } else {
            // Add the job to the matchRankJobsStore with the original job ID
            const { addJob } = useMatchRankJobsStore.getState();

            addJob({
              jobId, // Use the original job ID
              status: 'queued',
              message: 'Match and rank process started...',
            });

            // Log the current state of the matchRankJobsStore for debugging
          }
        } catch (error) {
          console.error('Error starting match and rank process:', error);

          // Check if this is a credit-related error
          // The apiHelper should have already shown the credit modal for 403 errors
          // but we should handle the error gracefully here
          let errorMessage = 'Failed to start match and rank process';

          if (error && typeof error === 'object') {
            // Check if it's an API error with a message
            if ('message' in error && typeof error.message === 'string') {
              // If it's a credit-related error, the modal should already be shown
              if (
                error.message.toLowerCase().includes('credit') ||
                error.message.toLowerCase().includes('insufficient')
              ) {
                errorMessage = error.message;
              } else {
                errorMessage = error.message;
              }
            }

            // Check if it's a 402 error (Payment Required - credits)
            if ('status' in error && error.status === 402) {
              // Don't show toast for credit errors as the modal is more appropriate
              jobsStore.removePendingRequest(matchRankRequestKey);
              setIsProcessing(false);
              return;
            }
          }

          showToast({
            message: errorMessage,
            isSuccess: false,
          });
          jobsStore.removePendingRequest(matchRankRequestKey);
          setIsProcessing(false);
          return;
        } finally {
          jobsStore.removePendingRequest(matchRankRequestKey);
        }
      } else {
        await jobsStore.waitForPendingRequest(matchRankRequestKey);
      }

      // We don't show a toast here because the MatchRankStatusManager will handle that
      setShowUploader(false);
    } catch (error) {
      console.error('Error in ranking process:', error);
      showToast({
        message: 'Failed to complete ranking process',
        isSuccess: false,
      });
    } finally {
      setIsProcessing(false);
    }
  },

  refreshJobData: async (jobId, forceRefresh = false, mode = 'view'): Promise<IJob> => {
    // Get current state
    const state = get();
    const now = Date.now();
    const lastRefreshTime = state.lastJobDataRefreshTimestamps[jobId] || 0;
    const refreshInterval = 60 * 1000; // 1 minute

    // Create a unique key for this request to track pending requests
    const requestKey = `refresh_job_${jobId}_${forceRefresh ? 'force' : 'normal'}`;

    // Check if there's already a pending request for this job - lazy import to avoid circular dependency
    const { useJobsStore } = await import('@/stores/unifiedJobStore');
    const jobsStore = useJobsStore.getState();
    if (jobsStore.hasPendingRequest && jobsStore.hasPendingRequest(requestKey)) {
      return await jobsStore.waitForPendingRequest(requestKey);
    }

    // Skip cache checks if forceRefresh is true
    if (!forceRefresh) {
      // First check if we have a cached job in jobsStore
      const jobsStoreSelectedJob = jobsStore.selectedJob;
      if (jobsStoreSelectedJob && jobsStoreSelectedJob.id === jobId) {
        // Convert JobData to IJob - lazy import to avoid circular dependency
        const { jobDataToIJob } = await import('@/stores/unifiedJobStore');
        const jobAsIJob = jobDataToIJob(jobsStoreSelectedJob);

        // Update our local cache with this job data
        state.cacheJob(jobAsIJob);

        // If this is the currently selected job, update it
        if (state.selectedJobId === jobId) {
          set({ selectedJob: jobAsIJob });
        }

        // Update candidate count if needed
        if (jobsStoreSelectedJob.candidates) {
          set({ candidateCount: jobsStoreSelectedJob.candidates.length });
        }

        // Update the timestamp to prevent immediate re-fetching
        set(state => ({
          lastJobDataRefreshTimestamps: {
            ...state.lastJobDataRefreshTimestamps,
            [jobId]: now,
          },
        }));

        return jobAsIJob;
      }

      // Also check our local cache for recent data
      const cachedJob = state.getJobById(jobId);
      if (cachedJob && now - lastRefreshTime < refreshInterval) {
        // If we have recent cached data, use it instead of making an API call
        if (state.selectedJobId === jobId) {
          set({ selectedJob: cachedJob });
        }
        return cachedJob;
      }
    }

    try {
      // Check for active file uploads for this job
      const uploadJobsStore = window['uploadJobsStore'];
      const hasActiveUploads =
        uploadJobsStore &&
        uploadJobsStore.getJobsByRelatedId &&
        uploadJobsStore
          .getJobsByRelatedId(jobId)
          ?.some((job: any) => job.status === 'queued' || job.status === 'active');

      // Skip refresh if there are active uploads and we're not forcing
      if (hasActiveUploads && !forceRefresh) {
        return state.getJobById(jobId);
      }

      // Explicitly remove the job cache before fetching
      if (typeof localStorage !== 'undefined') {
        const jobCacheKey = `api_cache_/jobs/${jobId}`;
        localStorage.removeItem(jobCacheKey);
        // Also clear criteria cache if using lightweight endpoint
        if (mode === 'edit') {
          const criteriaCacheKey = `api_cache_/jobs/${jobId}/criteria`;
          localStorage.removeItem(criteriaCacheKey);
        }
      }
      return null;
    } catch (error) {
      console.error(`Error refreshing job data for job ${jobId}:`, error);
      showToast({
        message: 'Failed to refresh job data',
        isSuccess: false,
      });

      return null;
    }
  },

  refreshAfterScouting: async jobId => {
    try {
      // Force refresh the job data to get the latest candidates (always use full data for scouting)
      const job = await get().refreshJobData(jobId, true, 'view');

      if (job) {
        // If the job is currently selected, update the view
        if (get().selectedJobId === jobId) {
          // Update the selected job with the refreshed data
          set({ selectedJob: job });

          // Update candidate count
          if (job.candidates) {
            set({ candidateCount: job.candidates.length });
          }
        }

        // Update the job state store with the refreshed job data
        if (typeof window !== 'undefined') {
          const { useJobStateStore } = await import('@/stores/unifiedJobStore');
          useJobStateStore.getState().markJobAsUpdated(jobId);
        }

        return;
      }
    } catch (error) {
      console.error(`Error refreshing job ${jobId} after scouting:`, error);
      showToast({
        message: 'Failed to refresh job data after scouting',
        isSuccess: false,
      });
    }
  },

  updateCandidateStatus: async (candidateId, status, jobId, dateSensitiveInfo) => {
    const state = get();

    try {
      const requestData = {
        status,
        ...(jobId ? { jobId } : {}), // Include jobId if provided
        ...(dateSensitiveInfo ? { dateSensitiveInfo } : {}), // Include date information if provided
      };

      const response = await apiHelper.patch(`/candidates/${candidateId}/status`, requestData);

      if (response) {
        if (response.needsCulturalFitSetup) {
          return response;
        }

        if (state.selectedJob && state.selectedJob.id === jobId) {
          const updatedCandidates = state.selectedJob.candidates.map(c =>
            c.id === candidateId ? { ...c, status } : c
          );
          const updatedJob = { ...state.selectedJob, candidates: updatedCandidates };
          get().setSelectedJob(updatedJob);

          if (state.selectedCandidate && state.selectedCandidate.id === candidateId) {
            get().setSelectedCandidate({ ...state.selectedCandidate, status });
          }

          showToast({
            message: `Candidate status updated to ${status}`,
            isSuccess: true,
          });
        }
        return response;
      }
    } catch (error) {
      console.error('Error updating candidate status:', error);
      showToast({
        message: 'Failed to update candidate status',
        isSuccess: false,
      });
    }
    return null;
  },

  updateCandidateData: (candidateId, updatedData) => {
    const state = get();

    // Update candidate in the selected job's candidates array
    if (state.selectedJob && state.selectedJob.candidates) {
      const updatedCandidates = state.selectedJob.candidates.map(c =>
        c.id === candidateId ? { ...c, ...updatedData } : c
      );
      const updatedJob = { ...state.selectedJob, candidates: updatedCandidates };
      get().setSelectedJob(updatedJob);

      // Also update the cache
      get().cacheJob(updatedJob);
    }

    // Update the selected candidate if it matches
    if (state.selectedCandidate && state.selectedCandidate.id === candidateId) {
      get().setSelectedCandidate({ ...state.selectedCandidate, ...updatedData });
    }

    // Force refresh the job data to ensure we have the latest from backend
    if (state.selectedJobId) {
      // Clear the timestamp to force a refresh on next access
      set(state => ({
        lastJobDataRefreshTimestamps: {
          ...state.lastJobDataRefreshTimestamps,
          [state.selectedJobId]: 0,
        },
      }));
    }
  },

  checkCulturalFitSetup: response => {
    return response && typeof response === 'object' && 'needsCulturalFitSetup' in response;
  },

  handleEditClick: () => {
    set({ viewMode: 'edit' });
  },

  handleUploaderClose: () => {
    const state = get();
    state.setIsProcessing(false);
    state.setShowUploader(false);
  },

  // Combined actions
  openMatchRankDetails: (job, mode = 'view') => {
    // Use the passed mode directly, don't override based on job status
    // The caller should determine the correct mode based on URL parameters
    const effectiveMode = mode;

    // Get current state to check if we're changing jobs
    const currentState = get();
    const isChangingJobs = currentState.selectedJobId !== job.id;

    // Cache the job and set it as selected
    set(state => ({
      isOpen: true,
      selectedJobId: job.id,
      selectedJob: job,
      // Reset selected candidate when changing jobs to prevent showing candidates from previous job
      selectedCandidate: isChangingJobs ? null : state.selectedCandidate,
      viewMode: effectiveMode,
      candidateCount: job.candidates?.length || 0,
      jobCache: {
        ...state.jobCache,
        [job.id]: job,
      },
    }));
  },

  closeMatchRankDetails: () =>
    set({
      isOpen: false,
      selectedJobId: null,
      selectedJob: null,
      viewMode: 'view',
      showUploader: false,
      isUploadMode: false,
      isProcessing: false,
      hasFormChanges: false,
      hasFileUploads: false,
      lastRankedCandidateCount: 0,
      jobDescriptionChanged: false,
    }),

  // URL parameter utilities
  clearUrlParameter: (paramName: string, searchParams?: URLSearchParams) => {
    // Use provided searchParams or create from current URL
    const params = searchParams
      ? new URLSearchParams(searchParams.toString())
      : new URLSearchParams(typeof window !== 'undefined' ? window.location.search : '');

    // Remove the specified parameter
    params.delete(paramName);
    params.delete('mode');
    params.delete('showCultureFit');
    params.delete('showVideoJd');
    params.delete('jobId');

    // Return the new URL path with updated parameters
    return `${params.toString() ? `?${params.toString()}` : ''}`;
  },

  clearUrlParameters: (paramNames: string[], searchParams?: URLSearchParams) => {
    // Use provided searchParams or create from current URL
    const params = searchParams
      ? new URLSearchParams(searchParams.toString())
      : new URLSearchParams(typeof window !== 'undefined' ? window.location.search : '');

    // Remove all specified parameters
    paramNames.forEach(paramName => params.delete(paramName));
    params.delete('mode');
    params.delete('showCultureFit');
    params.delete('showVideoJd');
    params.delete('jobId');

    // Return the new URL path with updated parameters
    return `${params.toString() ? `?${params.toString()}` : ''}`;
  },
}));

// Selectors for computed values
export const selectIsResumeUploadActive = (state: MatchRankDetailsState) =>
  state.showUploader && state.isUploadMode;

export const selectIsMatched = (state: MatchRankDetailsState) =>
  state.selectedJob?.status === JobStatus.MATCHED;

export const selectHasFormEdits = (state: MatchRankDetailsState) =>
  state.hasFormChanges || state.hasFileUploads;

// Selector to check if MatchRankStatusManager has any active jobs
export const selectHasActiveMatchRankJobs = () => {
  const matchRankJobsStore = useMatchRankJobsStore.getState();
  return (
    matchRankJobsStore.activeJobs.length > 0 ||
    Object.values(matchRankJobsStore.jobs).some(
      job => job.status === 'queued' || job.status === 'active'
    )
  );
};

// Selector to check if there are unranked candidates (candidates without evaluation for the current job)
export const selectHasUnrankedCandidates = (state: MatchRankDetailsState) => {
  if (!state.selectedJob?.candidates) {
    return false;
  }

  // Use the same logic as backend: check candidateEvaluations table for this specific job
  return state.selectedJob.candidates.some(candidate => {
    // Check if this candidate already has an evaluation for this specific job
    const hasEvaluationForThisJob = state.selectedJob?.candidateEvaluations?.some(
      (evaluation: { candidateId: string; jobId: string }) =>
        evaluation.candidateId === candidate.id && evaluation.jobId === state.selectedJob?.id
    );
    return !hasEvaluationForThisJob;
  });
};

// Selector for calculating credit cost based on change type
export const selectCreditCost = (state: MatchRankDetailsState) => {
  const {
    candidateCount,
    lastRankedCandidateCount,
    jobDescriptionChanged,
    hasFormChanges,
    hasFileUploads,
    viewMode,
    showUploader,
  } = state;

  // If no changes at all, check if we're in edit mode with unranked candidates or if uploader is showing candidates
  if (!hasFormChanges && !hasFileUploads) {
    // In edit mode with unranked candidates, we should charge for initial ranking
    if (viewMode === 'edit' && selectHasUnrankedCandidates(state)) {
      return candidateCount;
    }
    // If uploader is showing candidates, we should charge for ranking them
    if (showUploader && candidateCount > 0) {
      return candidateCount;
    }
    return 0;
  }

  // If job description/requirements changed, charge for all candidates
  if (jobDescriptionChanged) {
    return candidateCount;
  }

  // If only new candidates added (file uploads but no job description changes),
  // charge only for new candidates
  if (hasFileUploads && !jobDescriptionChanged) {
    const newCandidates = Math.max(0, candidateCount - lastRankedCandidateCount);
    return newCandidates;
  }

  // If only threshold changes (hasFormChanges but not jobDescriptionChanged), cost is 0
  if (hasFormChanges && !jobDescriptionChanged && !hasFileUploads) {
    return 0;
  }

  // Default fallback
  return candidateCount;
};
