import './globals.css';

import { Metadata } from 'next';
import { Inter } from 'next/font/google';

// Import components
import AppErrorBoundary from '@/components/AppErrorBoundary';
import AuthInitializer from '@/components/AuthInitializer';
import AuthStateHandler from '@/components/AuthStateHandler';
import { CustomToasterContainer } from '@/components/CustomToaster';
import HMRErrorBoundary from '@/components/HMRErrorBoundary';
import HMRErrorHandler from '@/components/HMRErrorHandler';
import StatusCompletionModalManager from '@/components/shared/StatusCompletionModalManager';
import { AuthModalProvider } from '@/contexts/AuthModalContext';
import { CreditPurchaseModalProvider } from '@/contexts/CreditPurchaseModalContext';
import { ErrorModalProvider } from '@/contexts/ErrorModalContext';

import ApprovalStatusProvider from '@/providers/ApprovalStatusProvider';
import ConditionalJobsProvider from '@/providers/ConditionalJobsProvider';
import ClarityProvider from '@/providers/ClarityProvider';
import IntercomProvider from '@/providers/IntercomProvider';
import QueryProvider from '@/providers/QueryProvider';
import { UserProvider } from '@auth0/nextjs-auth0/client';

// Load polyfills in client components only
if (typeof window !== 'undefined') {
  import('@/lib/turbopack-polyfills').catch(e => {
    console.warn('Failed to load polyfills:', e);
  });
}

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Kaleido Talent | Job Description Creation',
  description: 'Finding the right candidate for your company',
  icons: {
    icon: '/favicon.ico',
  },
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                try {
                  // Only run this script on the client side
                  if (typeof window !== 'undefined') {
                    // Get the saved theme from localStorage
                    const savedTheme = localStorage.getItem('theme');

                    // Check system preference
                    const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

                    // Determine which theme to set
                    let themeToSet = 'dark'; // Default theme

                    if (savedTheme === 'dark') {
                      themeToSet = 'dark';
                    } else if (savedTheme === 'dark') {
                      themeToSet = 'dark';
                    } else if (!savedTheme && !systemPrefersDark) {
                      themeToSet = 'dark';
                    }

                    // Apply the theme
                    document.documentElement.setAttribute('data-theme', themeToSet);
                    document.documentElement.style.colorScheme = themeToSet;

                    // Store the initial theme if not already stored
                    if (!savedTheme) {
                      localStorage.setItem('theme', 'dark');
                    }
                  }
                } catch (e) {
                  console.error('Error setting initial theme:', e);
                }
              })();
            `,
          }}
        />
      </head>
      <body className={inter.className}>
        <HMRErrorHandler />
        <HMRErrorBoundary>
          <AppErrorBoundary>
            <UserProvider>
              <QueryProvider>
                <ErrorModalProvider>
                  <AuthModalProvider>
                    <CreditPurchaseModalProvider>
                      <AuthStateHandler>
                        <ApprovalStatusProvider>
                          <ConditionalJobsProvider>
                            <ClarityProvider>
                              <IntercomProvider>
                                {/* Add AuthInitializer to detect and mark auth initialization */}
                                <AuthInitializer />
                                {/* Add ApprovalCheck component to force approval check on every page */}
                                {children}
                                {/* Toast notifications */}
                                <CustomToasterContainer />
                                {/* Enhanced status completion modals */}
                                <StatusCompletionModalManager />
                              </IntercomProvider>
                            </ClarityProvider>
                          </ConditionalJobsProvider>
                        </ApprovalStatusProvider>
                      </AuthStateHandler>
                    </CreditPurchaseModalProvider>
                  </AuthModalProvider>
                </ErrorModalProvider>
              </QueryProvider>
            </UserProvider>
          </AppErrorBoundary>
        </HMRErrorBoundary>
        {/* Portal root for modals and other portal content */}
        <div id="portal-root" />
      </body>
    </html>
  );
}
