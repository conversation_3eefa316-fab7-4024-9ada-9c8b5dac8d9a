import { Column, <PERSON>tity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm';

import {
  CultureFitQuestions,
  JobMetrics,
  JobStatus,
  JobTLDR,
  TypeOfHiring,
  TypeOfJob,
} from '@/shared/types/job.types';
import { Candidate, Company, Notification, VideoJD, VideoResponse } from '@modules/entities';
import { JobApplication } from '@modules/job-seeker/entities/job-application.entity';
import { BaseEntity } from '@shared/entities/base.entity';

import { CandidateApplication } from '../../candidate/entities/candidate-application.entity';
import { CandidateEvaluation } from '../../candidate/entities/candidate-evaluation.entity';

export enum ExperienceLevel {
  GRADUATE = 'graduate',
  JUNIOR = 'junior',
  MID = 'mid',
  SENIOR = 'senior',
  LEAD = 'lead',
}

@Entity('jobs')
@Index('idx_jobs_client_id', ['clientId'])
@Index('idx_jobs_status', ['status'])
@Index('idx_jobs_is_published', ['isPublished'])
@Index('idx_jobs_experience_level', ['experienceLevel'])
@Index('idx_jobs_client_status_published', ['clientId', 'status', 'isPublished'])
@Index('idx_jobs_created_at', ['createdAt'])
@Index('idx_jobs_is_graduate_role', ['isGraduateRole'])
@Index('idx_jobs_external_id', ['externalId'])
@Index('idx_jobs_source', ['source'])
export class Job extends BaseEntity {
  @Column({ type: 'varchar', nullable: false })
  companyName!: string;

  @Column({ type: 'varchar', nullable: true, unique: true })
  slug?: string;

  @Column({ type: 'text', nullable: true })
  companyDescription?: string;

  @Column({ type: 'varchar', nullable: false })
  jobType!: string;

  @Column({ type: 'varchar', nullable: false, default: 'backfill' })
  department!: string;

  // External ID from ATS or other source
  @Column({ type: 'varchar', nullable: true })
  externalId?: string;

  // Source system (ats, manual, import)
  @Column({ type: 'varchar', nullable: true })
  source?: string;

  // Source type (Lever, Greenhouse, etc)
  @Column({ type: 'varchar', nullable: true })
  sourceType?: string;

  // Virtual property to store uploaded resume filenames
  uploadedResumes?: string[];

  @Column({
    type: 'enum',
    enum: ExperienceLevel,
    default: ExperienceLevel.MID,
  })
  experienceLevel!: ExperienceLevel;

  @Column({ type: 'boolean', default: false })
  isGraduateRole!: boolean;

  @Column('text', { array: true, default: [] })
  graduateRequirements?: string[];

  @Column({ type: 'varchar', nullable: true })
  experience?: string;

  @Column({ type: 'varchar', nullable: true })
  hiringManagerDescription?: string;

  @Column({ type: 'varchar', nullable: true })
  currency?: string;

  @Column({ type: 'varchar', nullable: true })
  salaryRange?: string;

  @Column({ type: 'text', nullable: true })
  finalDraft?: string;

  @Column({ type: 'varchar', nullable: true })
  generatedJDTone?: string;

  @Column({ type: 'text', nullable: true })
  generatedJD?: string;

  @Column({ type: 'varchar', length: 280, nullable: true })
  socialMediaDescription?: string;

  @Column('text', { array: true, default: [] })
  skills?: string[];

  @Column('text', { array: true, default: [] })
  requirements?: string[];

  @Column({ type: 'varchar', nullable: true })
  paymentPeriod?: string;

  @Column({
    type: 'enum',
    enum: TypeOfHiring,
    nullable: true,
  })
  typeOfHiring?: TypeOfHiring;

  @Column({
    type: 'enum',
    enum: TypeOfJob,
    nullable: true,
  })
  typeOfJob?: TypeOfJob;

  @Column({
    type: 'enum',
    enum: JobStatus,
    default: JobStatus.NEW,
  })
  status!: JobStatus;

  @Column({
    default: false,
  })
  isPublished?: boolean;

  // Job metrics tracking
  @Column({
    type: 'jsonb',
    nullable: true,
    default: {
      views: 0,
      applications: 0,
      viewSources: {},
      applicationSources: {},
      matchScores: {},
    },
  })
  metrics?: JobMetrics;

  @ManyToOne(() => Company, (company: Company) => company.jobs, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  @JoinColumn({ name: 'clientId', referencedColumnName: 'clientId' })
  company?: Company;

  @OneToMany(() => Candidate, (candidate: Candidate) => candidate.job, {
    cascade: true,
    onDelete: 'CASCADE',
    eager: true,
  })
  candidates?: Candidate[];

  @OneToMany(() => VideoResponse, (videoResponse: VideoResponse) => videoResponse.job, {
    cascade: true,
    onDelete: 'CASCADE',
    eager: true,
  })
  videoResponses?: VideoResponse[];

  @OneToMany(() => VideoJD, (videoJD: VideoJD) => videoJD.job, {
    cascade: true,
    onDelete: 'CASCADE',
    eager: true,
  })
  videoJDs?: VideoJD[];

  @OneToMany(() => Notification, (notification: Notification) => notification.job, {
    cascade: true,
    onDelete: 'CASCADE',
    eager: true,
  })
  notifications?: Notification[];

  @OneToMany(() => CandidateEvaluation, (evaluation: CandidateEvaluation) => evaluation.job, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  candidateEvaluations?: CandidateEvaluation[];

  @Column('text', { array: true, default: [] })
  jobResponsibilities?: string[];

  @Column({ type: 'text', nullable: true })
  cultureFitDescription?: string;

  @Column({ type: 'jsonb', nullable: true })
  cultureFitQuestions?: CultureFitQuestions[];

  @Column('text', { array: true, default: [] })
  companyValues?: string[];

  @Column('text', { array: true, default: [] })
  culturalFit?: string[];

  @Column('text', { array: true, default: [] })
  education?: string[];

  @Column('text', { array: true, default: [] })
  language?: string[];

  @Column('text', { array: true, default: [] })
  softSkills?: string[];

  @Column('text', { array: true, default: [] })
  location?: string[];

  @Column('text', { array: true, default: [] })
  benefits?: string[];

  @Column('text', { array: true, default: [] })
  careerGrowth?: string[];

  @Column({ type: 'decimal', nullable: true, precision: 5, scale: 2 })
  topCandidateThreshold?: number;

  @Column({ type: 'decimal', nullable: true, precision: 5, scale: 2 })
  secondTierCandidateThreshold?: number;

  @OneToMany(() => JobApplication, (application) => application.job)
  applications?: JobApplication[];

  @Column({ type: 'jsonb', nullable: true })
  tldr?: JobTLDR;

  @OneToMany(() => CandidateApplication, (application) => application.job)
  candidateApplications?: CandidateApplication[];

  // Virtual property for totalCandidates count (populated by loadRelationCountAndMap)
  totalCandidates?: number;

  // Add a method to check if the job is complete
  isComplete(): boolean {
    return !!(
      this.companyName &&
      this.jobType &&
      this.department &&
      this.companyDescription &&
      this.department
    );
  }

  // Add a method to check if the job is suitable for graduates
  isGraduateSuitable(): boolean {
    return (
      this.isGraduateRole ||
      this.experienceLevel === ExperienceLevel.GRADUATE ||
      this.experienceLevel === ExperienceLevel.JUNIOR
    );
  }

  /**
   * Checks if critical job criteria have changed that would require re-evaluation of candidates
   * @param oldJob Previous state of the job
   * @returns boolean indicating if critical criteria have changed
   */
  hasCriticalCriteriaChanged(oldJob: Job): boolean {
    // Check if any of the critical matching criteria have changed
    return (
      JSON.stringify(this.skills) !== JSON.stringify(oldJob.skills) ||
      JSON.stringify(this.requirements) !== JSON.stringify(oldJob.requirements) ||
      JSON.stringify(this.jobResponsibilities) !== JSON.stringify(oldJob.jobResponsibilities) ||
      JSON.stringify(this.softSkills) !== JSON.stringify(oldJob.softSkills) ||
      this.finalDraft !== oldJob.finalDraft ||
      this.jobType !== oldJob.jobType ||
      this.department !== oldJob.department ||
      this.experienceLevel !== oldJob.experienceLevel ||
      this.topCandidateThreshold !== oldJob.topCandidateThreshold ||
      this.secondTierCandidateThreshold !== oldJob.secondTierCandidateThreshold
    );
  }
}
