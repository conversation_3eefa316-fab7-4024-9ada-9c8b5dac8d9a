'use client';

import { motion } from 'framer-motion';
import { useState } from 'react';
import {
  ArrowLeft,
  BarChart3,
  BookOpen,
  Briefcase,
  Clock,
  DollarSign,
  Download,
  Eye,
  Share2,
  Sparkles,
  Target,
  TrendingUp,
} from 'lucide-react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useEffect } from 'react';

import ColourfulLoader from '@/components/Layouts/ColourfulLoader';
import SimpleJobSeekerLayout from '@/components/steps/layout/SimpleJobSeekerLayout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { InsightType } from '@/lib/career-insights/config';
import { useCareerInsightsStore } from '@/stores/careerInsightsStore';
import { UserRole } from '@/types/roles';
import AnalysisRenderer from '@/components/career-insights/AnalysisRenderer';
import ModernRecommendations from '@/components/career-insights/ModernRecommendations';
import { PDFDownloadLink } from '@react-pdf/renderer';
import CareerInsightsPdf from '@/components/pdf/CareerInsightsPdf';

const insightTypeConfig = {
  [InsightType.SKILL_GAP_ANALYSIS]: {
    icon: Target,
    color: 'from-blue-500 to-cyan-500',
    bgColor: 'bg-blue-500/10',
  },
  [InsightType.CAREER_PATH_RECOMMENDATION]: {
    icon: TrendingUp,
    color: 'from-purple-500 to-pink-500',
    bgColor: 'bg-purple-500/10',
  },
  [InsightType.MARKET_TREND_ANALYSIS]: {
    icon: BarChart3,
    color: 'from-green-500 to-emerald-500',
    bgColor: 'bg-green-500/10',
  },
  [InsightType.ROLE_TRANSITION_GUIDANCE]: {
    icon: Briefcase,
    color: 'from-orange-500 to-red-500',
    bgColor: 'bg-orange-500/10',
  },
  [InsightType.COMPENSATION_BENCHMARK]: {
    icon: DollarSign,
    color: 'from-yellow-500 to-amber-500',
    bgColor: 'bg-yellow-500/10',
  },
};

export default function CareerInsightDetailPage() {
  const router = useRouter();
  const params = useParams();
  const id = params?.id as string;
  const [activeTab, setActiveTab] = useState('analysis');

  const { currentInsight, isLoading, fetchInsightById } = useCareerInsightsStore();

  useEffect(() => {
    if (id) {
      fetchInsightById(id);
    }
  }, [id, fetchInsightById]);

  if (isLoading || !currentInsight) {
    return (
      <SimpleJobSeekerLayout>
        <div className="flex-1 flex items-center justify-center">
          <ColourfulLoader />
        </div>
      </SimpleJobSeekerLayout>
    );
  }

  const config = insightTypeConfig[currentInsight.type];
  const Icon = config.icon;

  return (
    <SimpleJobSeekerLayout>
      <div className="flex-1 px-4 py-6 md:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <Button
            variant="ghost"
            onClick={() => router.push('/career-insights')}
            className="mb-4 text-white hover:bg-white/10"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Insights
          </Button>

          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-4">
              <div className={`p-4 rounded-lg bg-gradient-to-br ${config.color} shadow-lg`}>
                <Icon className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-white mb-2">{currentInsight.title}</h1>
                <div className="flex items-center space-x-4 text-sm text-gray-400">
                  <span className="flex items-center">
                    <Clock className="w-4 h-4 mr-1" />
                    {new Date(currentInsight.createdAt).toLocaleDateString()}
                  </span>
                  <span className="flex items-center">
                    <Eye className="w-4 h-4 mr-1" />
                    {currentInsight.viewCount} views
                  </span>
                  {currentInsight.validUntil && (
                    <Badge variant="outline" className="text-gray-300 border-gray-600">
                      Valid until {new Date(currentInsight.validUntil).toLocaleDateString()}
                    </Badge>
                  )}
                </div>
              </div>
            </div>

            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="icon"
                className="text-white border-white/20 hover:bg-white/10"
                onClick={() => {
                  // Share functionality
                  if (navigator.share) {
                    navigator.share({
                      title: currentInsight.title,
                      text: currentInsight.summary,
                      url: window.location.href,
                    });
                  } else {
                    navigator.clipboard.writeText(window.location.href);
                    alert('Link copied to clipboard!');
                  }
                }}
              >
                <Share2 className="w-4 h-4" />
              </Button>
              <PDFDownloadLink
                document={<CareerInsightsPdf insight={currentInsight} />}
                fileName={`CareerInsights_${currentInsight.type}_${new Date().toISOString().split('T')[0]}.pdf`}
              >
                {({ loading }) => (
                  <Button
                    variant="outline"
                    size="icon"
                    className="text-white border-white/20 hover:bg-white/10"
                    disabled={loading}
                  >
                    <Download className="w-4 h-4" />
                  </Button>
                )}
              </PDFDownloadLink>
            </div>
          </div>
        </motion.div>

        {/* Summary Card */}
        {/* Summary Card only show if summary has actual content */}
        {currentInsight.summary &&
          currentInsight.summary !== 'Processing your career insight...' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="mb-8"
            >
              <Card className={`${config.bgColor} border-white/10 backdrop-blur-sm`}>
                <div className="p-6">
                  <h2 className="text-lg font-semibold text-white mb-3 flex items-center">
                    <Sparkles className="w-5 h-5 mr-2 text-yellow-400" />
                    Key Insights
                  </h2>
                  <p className="text-gray-300 leading-relaxed">{currentInsight.summary}</p>
                </div>
              </Card>
            </motion.div>
          )}

        {/* Detailed Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <div className="flex items-center justify-center mb-8">
              <div className="bg-white/5 backdrop-blur-sm rounded-full p-1 flex relative">
                <TabsList className="bg-transparent border-0 p-0 flex">
                  <TabsTrigger
                    value="analysis"
                    className="relative px-6 py-3 rounded-full flex items-center space-x-2 transition-all duration-300 text-gray-400 hover:text-gray-200 data-[state=active]:text-white z-10"
                  >
                    {activeTab === 'analysis' && (
                      <motion.div
                        layoutId="activeTab"
                        className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full backdrop-blur-sm"
                        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                      />
                    )}
                    <BookOpen className="w-4 h-4 relative" />
                    <span className="text-sm font-medium relative">Detailed Analysis</span>
                  </TabsTrigger>
                  <TabsTrigger
                    value="recommendations"
                    className="relative px-6 py-3 rounded-full flex items-center space-x-2 transition-all duration-300 text-gray-400 hover:text-gray-200 data-[state=active]:text-white z-10"
                  >
                    {activeTab === 'recommendations' && (
                      <motion.div
                        layoutId="activeTab"
                        className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full backdrop-blur-sm"
                        transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                      />
                    )}
                    <Target className="w-4 h-4 relative" />
                    <span className="text-sm font-medium relative">Recommendations</span>
                  </TabsTrigger>
                  {currentInsight.type === InsightType.SKILL_GAP_ANALYSIS && (
                    <TabsTrigger
                      value="skills"
                      className="relative px-6 py-3 rounded-full flex items-center space-x-2 transition-all duration-300 text-gray-400 hover:text-gray-200 data-[state=active]:text-white z-10"
                    >
                      {activeTab === 'skills' && (
                        <motion.div
                          layoutId="activeTab"
                          className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-full backdrop-blur-sm"
                          transition={{ type: 'spring', stiffness: 300, damping: 30 }}
                        />
                      )}
                      <BarChart3 className="w-4 h-4 relative" />
                      <span className="text-sm font-medium relative">Skills Breakdown</span>
                    </TabsTrigger>
                  )}
                </TabsList>
              </div>
            </div>

            <TabsContent value="analysis" className="space-y-6">
              <AnalysisRenderer
                content={currentInsight.detailedAnalysis || 'Analysis content will appear here...'}
              />
            </TabsContent>

            <TabsContent value="recommendations" className="space-y-6">
              {currentInsight.aiInsights && (
                <ModernRecommendations aiInsights={currentInsight.aiInsights} />
              )}
            </TabsContent>

            {currentInsight.type === InsightType.SKILL_GAP_ANALYSIS &&
              currentInsight.skillGapAnalysis && (
                <TabsContent value="skills" className="space-y-6">
                  <Card className="bg-white/5 border-white/10 backdrop-blur-sm">
                    <div className="p-6">
                      <h3 className="text-xl font-semibold text-white mb-4">Skills Analysis</h3>
                      <div className="mb-6">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-gray-300">Overall Readiness</span>
                          <span className="text-white font-semibold">
                            {currentInsight.skillGapAnalysis.overallReadinessScore}%
                          </span>
                        </div>
                        <Progress
                          value={currentInsight.skillGapAnalysis.overallReadinessScore}
                          className="h-2 bg-white/10"
                        />
                      </div>

                      {currentInsight.skillGapAnalysis.skillGaps?.map(
                        (skill: any, index: number) => (
                          <div key={index} className="mb-6 p-4 bg-white/5 rounded-lg">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-semibold text-white">{skill.skillName}</h4>
                              <Badge
                                variant={
                                  skill.priority === 'HIGH'
                                    ? 'destructive'
                                    : skill.priority === 'MEDIUM'
                                      ? 'default'
                                      : 'secondary'
                                }
                              >
                                {skill.priority} Priority
                              </Badge>
                            </div>
                            <div className="text-sm text-gray-300 mb-2">
                              Current: {skill.currentLevel} → Target: {skill.targetLevel}
                            </div>
                            <p className="text-xs text-gray-400">
                              Estimated time to achieve: {skill.timeToAchieve}
                            </p>
                          </div>
                        )
                      )}
                    </div>
                  </Card>
                </TabsContent>
              )}
          </Tabs>
        </motion.div>
      </div>
    </SimpleJobSeekerLayout>
  );
}
