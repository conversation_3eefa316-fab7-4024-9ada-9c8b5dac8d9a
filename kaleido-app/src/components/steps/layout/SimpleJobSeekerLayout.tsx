'use client';

import '../../../styles/theming.css';

import { NavItem, getGradientByPath, getNavItemsByRole } from '@/lib/navigation';
import { AnimatePresence, motion } from 'framer-motion';
import { usePathname, useRouter } from 'next/navigation';
import React, { Suspense, useEffect, useMemo, useState } from 'react';

import { Header } from '@/components/layout/Header';
import { MobileMenu } from '@/components/layout/MobileMenu';
import { Sidebar } from '@/components/layout/Sidebar';
import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import { SIDEBAR } from '@/constants/layout';
import { useAuth0Token } from '@/hooks/useAuth0Token';
import { UserRole } from '@/types/roles';

interface SimpleJobSeekerLayoutProps {
  children: React.ReactNode;
  isLoading?: boolean;
}

// Gradient background component
const GradientBackground = () => {
  return (
    <motion.div
      className={`fixed inset-0 z-0 bg-[radial-gradient(ellipse_at_top_left,_var(--tw-gradient-stops))] from-[var(--background-start)] via-[var(--background-middle)] to-[var(--background-end)] after:absolute after:inset-0 after:bg-[radial-gradient(circle_at_70%_-20%,var(--background-accent),transparent_70%)] after:mix-blend-soft-light`}
      initial={false}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
    />
  );
};

const SimpleJobSeekerLayout: React.FC<SimpleJobSeekerLayoutProps> = ({ children, isLoading = false }) => {
  const { user, isLoading: userLoading } = useAuth0Token();
  const pathname = usePathname() ?? '/';
  const router = useRouter();

  const [expanded, setExpanded] = useState(() => {
    if (typeof window !== 'undefined') {
      const savedExpanded = localStorage.getItem('sidebarExpanded');
      return savedExpanded !== null ? savedExpanded === 'true' : false;
    }
    return false;
  });

  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [windowWidth, setWindowWidth] = useState(
    typeof window !== 'undefined' ? window.innerWidth : 0
  );

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Filter navigation items based on user role
  const filteredNavItems = useMemo(() => {
    const items = getNavItemsByRole(UserRole.JOB_SEEKER);
    return items as NavItem[];
  }, []);

  // Optimize layout transitions
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Prevent layout shift during hydration
  if (!mounted) {
    return null;
  }

  // If still loading user info, show loading state
  if (userLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <ColorfulSmokeyOrbLoader />
      </div>
    );
  }

  return (
    <div className="relative min-h-screen bg-transparent">
      <AnimatePresence mode="wait">
        <GradientBackground />
      </AnimatePresence>

      <Suspense fallback={<ColorfulSmokeyOrbLoader text="Loading..." useModalBg={false} />}>
        <motion.div
          className="relative bg-transparent"
          initial={false}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          <div className="min-h-screen flex bg-transparent">
            {/* Hide sidebar on mobile/medium devices */}
            <div className="hidden lg:block">
              <Sidebar
                expanded={expanded}
                setExpanded={setExpanded}
                pathname={pathname}
                navItems={filteredNavItems}
              />
            </div>

            <MobileMenu
              isMobileMenuOpen={isMobileMenuOpen}
              setIsMobileMenuOpen={setIsMobileMenuOpen}
              pathname={pathname}
              navItems={filteredNavItems}
              expanded={expanded}
              setExpanded={setExpanded}
            />

            {/* Main Content Area - Adjusted for sidebar width */}
            <div className="flex-1 h-screen bg-transparent">
              <motion.div
                initial={false}
                animate={{
                  marginLeft:
                    windowWidth < 1024
                      ? '0px'
                      : expanded
                        ? SIDEBAR.EXPANDED_WIDTH
                        : SIDEBAR.COLLAPSED_WIDTH,
                }}
                transition={{
                  duration: 0.3,
                  type: 'spring',
                  damping: 25,
                  stiffness: 120,
                }}
                className="relative flex flex-col h-full bg-transparent"
                style={{
                  width:
                    windowWidth < 1024
                      ? '100%'
                      : `calc(100% - ${expanded ? SIDEBAR.EXPANDED_WIDTH : SIDEBAR.COLLAPSED_WIDTH})`,
                }}
              >
                <div className="relative flex flex-col h-full bg-transparent backdrop-filter backdrop-blur-none">
                  <Header
                    pathname={pathname}
                    navItems={filteredNavItems}
                    expanded={expanded}
                    setExpanded={setExpanded}
                  />

                  {/* Main content wrapper */}
                  <div className="flex-1 flex flex-row relative mt-2 overflow-hidden bg-transparent">
                    <motion.main
                      className="relative flex-1 flex flex-col h-full overflow-auto pr-0 bg-transparent"
                      style={{ maxWidth: '100%' }}
                    >
                      {children}
                    </motion.main>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </Suspense>
    </div>
  );
};

export default SimpleJobSeekerLayout;