'use client';

import { usePathname } from 'next/navigation';
import { JobsProvider } from '@/contexts/jobs/JobsContext';

interface ConditionalJobsProviderProps {
  children: React.ReactNode;
}

export default function ConditionalJobsProvider({ children }: ConditionalJobsProviderProps) {
  const pathname = usePathname();
  
  // Only wrap with JobsProvider for job creation related paths
  const shouldUseJobsProvider = pathname?.includes('/job-description-creation') || 
                               pathname?.includes('/job-create') ||
                               pathname?.includes('/job-edit');
  
  if (shouldUseJobsProvider) {
    return <JobsProvider>{children}</JobsProvider>;
  }
  
  return <>{children}</>;
}