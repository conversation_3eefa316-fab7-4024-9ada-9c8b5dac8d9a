'use client';

import React, { useEffect, useState } from 'react';

import { motion } from 'framer-motion';

import WaitlistsTable from '@/components/admin/WaitlistsTable';
import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import AppLayout from '@/components/steps/layout/AppLayout';
import { apiClient as apiHelper } from '@/lib/apiHelper';
// Import the module resolver to ensure polyfills are applied
import { applyPolyfillsSafely } from '@/lib/module-resolver';
import { useJobsStore } from '@/stores/unifiedJobStore';

// Default stats data
const defaultStats = {
  total: 0,
  vipCount: 0,
  regularCount: 0,
  recentSignups: 0,
};

export default function WaitlistsPage() {
  const [waitlistStats, setWaitlistStats] = useState(defaultStats);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { refreshJobs, invalidateJobsCache } = useJobsStore();

  // Apply polyfills on component mount
  useEffect(() => {
    applyPolyfillsSafely();
  }, []);

  useEffect(() => {
    const fetchWaitlistStats = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Call the backend endpoint to get waitlist statistics
        const response = await apiHelper.get('/waitlist/stats');
        if (response && typeof response === 'object') {
          setWaitlistStats(response as any);
        } else {
          throw new Error('Invalid response format');
        }
      } catch (error: any) {
        console.error('Error fetching waitlist stats:', error);
        setError(error.message || 'Failed to fetch waitlist statistics');
        // Keep the default stats (all zeros) instead of using mock data
        setWaitlistStats(defaultStats);
      } finally {
        setIsLoading(false);
      }
    };

    fetchWaitlistStats();
  }, []);

  return (
    <AppLayout>
      <div className="mx-auto w-full p-6">
        {isLoading ? (
          <ColorfulSmokeyOrbLoader text="Loading waitlist data..." useModalBg={false} />
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="space-y-6">
              {/* Page Header */}
              <div className="flex justify-between items-center">
                <h1 className="text-2xl font-bold text-white">Waitlist Management</h1>

                {/* Stats Summary */}
                <div className="flex gap-4">
                  <div className="bg-white/5 border border-white/10 rounded-lg px-4 py-2">
                    <div className="text-sm text-white/60">Total</div>
                    <div className="text-lg font-semibold text-white">{waitlistStats.total}</div>
                  </div>
                  <div className="bg-white/5 border border-white/10 rounded-lg px-4 py-2">
                    <div className="text-sm text-white/60">VIP</div>
                    <div className="text-lg font-semibold text-purple-300">
                      {waitlistStats.vipCount}
                    </div>
                  </div>
                  <div className="bg-white/5 border border-white/10 rounded-lg px-4 py-2">
                    <div className="text-sm text-white/60">Recent (7d)</div>
                    <div className="text-lg font-semibold text-green-300">
                      {waitlistStats.recentSignups}
                    </div>
                  </div>
                </div>
              </div>

              {/* Error Display */}
              {error && (
                <div className="p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
                  <p className="text-red-400">Error loading waitlist statistics: {error}</p>
                  <button
                    type="button"
                    onClick={() => {
                      // Refresh data through Zustand stores instead of full page reload
                      invalidateJobsCache();
                      refreshJobs(true);
                      // Clear error and retry loading
                      setError(null);
                      setIsLoading(true);
                    }}
                    className="mt-2 px-3 py-1 bg-red-500/20 text-red-300 rounded text-sm hover:bg-red-500/30 transition-colors"
                  >
                    Retry
                  </button>
                </div>
              )}

              {/* Waitlists Table */}
              <div className="mt-8">
                <h2 className="text-xl font-semibold text-white/90 mb-4">Waitlist Entries</h2>
                <WaitlistsTable />
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </AppLayout>
  );
}
