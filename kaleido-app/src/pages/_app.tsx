import '@/app/globals.css';
// Import polyfills for Turbopack compatibility
import '@/lib/turbopack-polyfills';

import { AppProps } from 'next/app';
import { useEffect } from 'react';
import { suppressAuth0LogoutErrors } from '@/lib/auth0-error-suppressor';

import { ATSManager } from '@/components/ATSManager';
import AuthGuard from '@/components/Auth/AuthGuard';
import AuthSyncProvider from '@/components/Auth/AuthSyncProvider';
import { PostLoginRoleHandler } from '@/components/Auth/PostLoginRoleHandler';
import { SessionManager } from '@/components/Auth/SessionManager';
import { CustomToasterContainer } from '@/components/CustomToaster';
import { ApiDebugger } from '@/components/Debug/ApiDebugger';
import ErrorBoundary from '@/components/ErrorBoundary';
import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import { ScoutJobsManager } from '@/components/ScoutJobsManager';
import { UploadJobsManager } from '@/components/UploadJobsManager';
import { JobsProvider } from '@/contexts/jobs/JobsContext';
import { NotificationProvider } from '@/contexts/NotificationContext';
import ApprovalStatusProvider from '@/providers/ApprovalStatusProvider';
import { UserProvider } from '@auth0/nextjs-auth0/client';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5,
      gcTime: 1000 * 60 * 30,
    },
  },
});

function MyApp({ Component, pageProps }: AppProps) {
  useEffect(() => {
    // Suppress expected Auth0 errors during logout
    suppressAuth0LogoutErrors();
  }, []);

  return (
    <ErrorBoundary>
      <UserProvider>
        <QueryClientProvider client={queryClient}>
          <AuthSyncProvider>
            <AuthGuard fallback={<ColorfulSmokeyOrbLoader />}>
              <NotificationProvider>
                <ApprovalStatusProvider>
                  <JobsProvider>
                    <Component {...pageProps} />
                    <CustomToasterContainer />
                    {/* Session manager for handling auth state */}
                    <SessionManager />
                    {/* Post-login role handler */}
                    <PostLoginRoleHandler>{null}</PostLoginRoleHandler>
                    {/* ATS background task manager */}
                    <ATSManager />
                    {/* Upload jobs manager for background uploads */}
                    <UploadJobsManager />
                    {/* Scout jobs manager for background scouting */}
                    {typeof window !== 'undefined' && <ScoutJobsManager />}
                    {/* API Debugger for development - set 'api_debug' to 'true' in localStorage to enable */}
                    {process.env.NODE_ENV === 'development' && typeof window !== 'undefined' && (
                      <ApiDebugger />
                    )}
                    {/* Portal root for modals and other portal content */}
                    <div id="portal-root" />
                  </JobsProvider>
                </ApprovalStatusProvider>
              </NotificationProvider>
            </AuthGuard>
          </AuthSyncProvider>
        </QueryClientProvider>
      </UserProvider>
    </ErrorBoundary>
  );
}

export default MyApp;
