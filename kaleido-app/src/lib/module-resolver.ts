/**
 * Module resolver for Next.js
 *
 * This file helps resolve module dependencies that might cause issues with
 * Next.js HMR (Hot Module Replacement) and Turbopack.
 */

import { reapplyPolyfills } from './turbopack-polyfills';

// Export a function to safely apply polyfills
export const applyPolyfillsSafely = () => {
  if (typeof window !== 'undefined') {
    try {
      reapplyPolyfills();
      return true;
    } catch (e) {
      console.warn('Failed to apply polyfills:', e);
      return false;
    }
  }
  return false;
};

// Export a function to check if we're in a browser environment
export const isBrowser = () => typeof window !== 'undefined';

// Export a function to safely import modules that might not be available
export const safeRequire = async (moduleName: string) => {
  // Only allow dynamic imports in Node.js environment to avoid bundler issues
  if (typeof window !== 'undefined') {
    console.warn(`Dynamic module loading not supported in browser environment for: ${moduleName}`);
    return null;
  }

  try {
    // We cannot use dynamic imports in Next.js builds as they cause critical dependency warnings
    // Return null and let the calling code handle the missing module
    console.warn(`Dynamic import not supported during build for: ${moduleName}`);
    return null;
  } catch (e) {
    console.warn(`Failed to import module ${moduleName}:`, e);
    return null;
  }
};

// Synchronous version for cases where async is not possible
export const safeRequireSync = (moduleName: string) => {
  // Only allow in Node.js environment
  if (typeof window !== 'undefined') {
    console.warn(
      `Synchronous module loading not supported in browser environment for: ${moduleName}`
    );
    return null;
  }

  try {
    // In server environment, this will be handled by Node.js
    // We return null as dynamic requires are not supported in Next.js builds
    console.warn(`Dynamic require not supported for: ${moduleName}`);
    return null;
  } catch (e) {
    console.warn(`Failed to require module ${moduleName}:`, e);
    return null;
  }
};

// Export default for module compatibility
const moduleExports = {
  applyPolyfillsSafely,
  isBrowser,
  safeRequire,
  safeRequireSync,
};

export default moduleExports;
