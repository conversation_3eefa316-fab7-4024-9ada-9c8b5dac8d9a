import { IJob } from '@/entities/interfaces';
import { useJobsStore, jobDataToIJob } from '@/stores/unifiedJobStore';
import { motion } from 'framer-motion';
import {
  ArrowLeft,
  Briefcase,
  Calendar,
  CheckCircle,
  ChevronRight,
  ChevronLeft,
  Clock,
  FileText,
  MapPin,
  Users,
  Menu,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { UnifiedJobView } from '../UnifiedJobView';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import Image from 'next/image';

interface UnifiedJobCompositionProps {
  jobId: string;
}

// Removed JobGroup interface - using flat list instead

const JobListItem: React.FC<{
  job: IJob;
  isSelected: boolean;
  onSelect: (job: IJob) => void;
  isCollapsed?: boolean;
}> = ({ job, isSelected, onSelect, isCollapsed = false }) => {
  const formatDate = (date: string | Date) => {
    const d = new Date(date);
    return d.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'NEW':
        return <Briefcase className="w-3.5 h-3.5 text-blue-400" />;
      case 'MATCHED':
        return <CheckCircle className="w-3.5 h-3.5 text-purple-400" />;
      case 'INTERVIEWING':
        return <Calendar className="w-3.5 h-3.5 text-yellow-400" />;
      case 'HIRED':
        return <CheckCircle className="w-3.5 h-3.5 text-green-400" />;
      default:
        return <FileText className="w-3.5 h-3.5 text-gray-400" />;
    }
  };

  const getCandidateCount = () => {
    // Use totalCandidates directly from job object first
    if (job.totalCandidates !== undefined) return job.totalCandidates;

    // Check candidateStats (this is what PostedJobList used to use)
    const stats = job['candidateStats'];
    if (stats?.totalCandidates !== undefined) return stats.totalCandidates;

    // Fallback to other fields
    if (job.matchedCandidatesCount !== undefined) return job.matchedCandidatesCount;
    if (job.candidatesCount !== undefined) return job.candidatesCount;
    if (job.candidates?.length) return job.candidates.length;
    return 0;
  };

  if (isCollapsed) {
    return (
      <Tooltip>
        <TooltipTrigger asChild>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.2 }}
            className={`
              group p-3 rounded-lg cursor-pointer transition-all duration-200 relative
              ${
                isSelected
                  ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-400/30 shadow-lg shadow-purple-500/10'
                  : 'border border-transparent hover:bg-gray-800/20 hover:border-gray-700/30'
              }
            `}
            onClick={() => onSelect(job)}
          >
            <div className="flex items-center justify-center">{getStatusIcon(job.status)}</div>
            {getCandidateCount() > 0 && (
              <div className="absolute top-0 right-0">
                <span className="inline-flex items-center justify-center w-5 h-5 text-[10px] font-bold bg-purple-500 text-white rounded-full border border-background">
                  {getCandidateCount()}
                </span>
              </div>
            )}
          </motion.div>
        </TooltipTrigger>
        <TooltipContent side="right">
          <div>
            <p className="font-medium">{job.jobType || job.jobTitle || 'Untitled Job'}</p>
            {job.location && (
              <p className="text-xs text-gray-400 mt-1">
                {Array.isArray(job.location) ? job.location[0] : job.location}
              </p>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.2 }}
      className={`
        group p-3 rounded-lg cursor-pointer transition-all duration-200
        ${
          isSelected
            ? 'bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-400/30 shadow-lg shadow-purple-500/10'
            : 'border border-transparent hover:bg-gray-800/20 hover:border-gray-700/30'
        }
      `}
      onClick={() => onSelect(job)}
    >
      <div className="flex items-center justify-between gap-3">
        <div className="flex items-start gap-3 flex-1 min-w-0">
          <div className="mt-0.5">{getStatusIcon(job.status)}</div>
          <div className="flex-1 min-w-0">
            <h4
              className={`font-medium text-sm truncate ${isSelected ? 'text-white' : 'text-gray-200'}`}
            >
              {job.jobType || job.jobTitle || 'Untitled Job'}
            </h4>
            {(job.createdAt ||
              (job.location &&
                (Array.isArray(job.location) ? job.location[0] : job.location)?.trim())) && (
              <div className="flex items-center gap-3 text-xs text-gray-400 mt-1">
                {job.createdAt && (
                  <div className="flex items-center gap-1">
                    <Clock className="w-3 h-3" />
                    <span>{formatDate(job.createdAt)}</span>
                  </div>
                )}
                {job.location &&
                  (Array.isArray(job.location) ? job.location[0] : job.location)?.trim() && (
                    <div className="flex items-center gap-1">
                      <MapPin className="w-3 h-3" />
                      <span className="truncate max-w-[100px]">
                        {Array.isArray(job.location) ? job.location[0] : job.location}
                      </span>
                    </div>
                  )}
              </div>
            )}
          </div>
        </div>

        {getCandidateCount() > 0 && (
          <div className="flex-shrink-0">
            <span className="inline-flex items-center gap-1 px-1.5 py-0.5 rounded text-[11px] bg-gray-800/20 text-gray-500 border border-gray-800/30">
              <Users className="w-2.5 h-2.5" />
              <span>{getCandidateCount()}</span>
            </span>
          </div>
        )}
      </div>
    </motion.div>
  );
};

// Removed JobSection component - using flat list instead

export const UnifiedJobComposition: React.FC<UnifiedJobCompositionProps> = ({ jobId }) => {
  const router = useRouter();
  const jobsByStatus = useJobsStore(state => {
    return state.jobsByStatus;
  });
  const fetchJobsByStatus = useJobsStore(state => state.fetchJobsByStatus);
  const isLoading = useJobsStore(state => state.isLoading);
  const selectedJob = useJobsStore(state => state.selectedJob);
  const selectedJobId = useJobsStore(state => state.selectedJobId);
  const setSelectedJobId = useJobsStore(state => state.setSelectedJobId);

  // Removed expandedSection - using flat list instead
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  // Fetch all jobs on mount
  useEffect(() => {
    // Fetch with includeCount to get candidate counts
    fetchJobsByStatus(1, 'ALL'); // Fetch up to 100 jobs to see all
  }, [fetchJobsByStatus]);

  // Set selected job
  useEffect(() => {
    if (jobId) {
      setSelectedJobId(jobId);
    }
  }, [jobId, setSelectedJobId]);

  // Debug selected job state
  useEffect(() => {}, [jobId, selectedJob, selectedJobId]);

  // Removed auto-expand logic - using flat list instead

  // Get all jobs as a flat list
  const allJobs = useMemo(() => {
    const jobs = jobsByStatus?.ALL?.jobs || [];
    return jobs;
  }, [jobsByStatus]);

  const handleJobSelect = useCallback(
    (job: IJob) => {
      router.push(`/jobs/${job.id}/manage`);
    },
    [router]
  );

  // Removed toggleSection - using flat list instead

  // Auto-collapse sidebar on smaller screens
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 1024) {
        setIsSidebarCollapsed(true);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <TooltipProvider>
      <div className="h-screen w-screen flex relative">
        {/* Mobile Menu Button */}
        <button
          onClick={() => setIsMobileSidebarOpen(!isMobileSidebarOpen)}
          className="fixed top-4 left-4 z-50 lg:hidden p-2 rounded-lg bg-gray-800/50 backdrop-blur-sm border border-gray-700/50"
        >
          <Menu className="w-5 h-5 text-gray-300" />
        </button>

        {/* Left Sidebar - Job List */}
        <div
          className={`
          ${isSidebarCollapsed ? 'w-20' : 'w-80'} 
          ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
          fixed lg:relative inset-y-0 left-0 z-40
          flex-shrink-0 border-r border-gray-300/10 bg-gray-900/95 lg:bg-gray-900/10 
          backdrop-blur-lg lg:backdrop-blur-none
          flex flex-col transition-all duration-300
        `}
        >
          <div>
            {/* Back to Jobs Button with collapse toggle */}
            <div
              className={`${isSidebarCollapsed ? 'px-3' : 'px-6'} py-6 border-b border-gray-300/10 flex items-center justify-between h-[80px]`}
            >
              {!isSidebarCollapsed ? (
                <>
                  <button
                    onClick={() => router.push('/jobs')}
                    className="flex items-center gap-2 text-muted-foreground hover:text-foreground transition-colors"
                  >
                    <ArrowLeft className="w-5 h-5" />
                    <span>Back to Jobs</span>
                  </button>
                  <button
                    onClick={() => setIsSidebarCollapsed(true)}
                    className="hidden lg:block p-1.5 rounded hover:bg-gray-800/20 transition-colors"
                  >
                    <ChevronLeft className="w-4 h-4 text-gray-400" />
                  </button>
                </>
              ) : (
                <div className="w-full flex flex-col items-center gap-2">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        onClick={() => router.push('/jobs')}
                        className="p-2 rounded hover:bg-gray-800/20 transition-colors"
                      >
                        <ArrowLeft className="w-4 h-4 text-gray-400" />
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="right">Back to Jobs</TooltipContent>
                  </Tooltip>
                  <button
                    onClick={() => setIsSidebarCollapsed(false)}
                    className="hidden lg:block p-1.5 rounded hover:bg-gray-800/20 transition-colors"
                  >
                    <ChevronRight className="w-4 h-4 text-gray-400" />
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Job List section - scrollable */}
          <div className="flex-1 overflow-y-auto overflow-x-visible">
            <div className={`${isSidebarCollapsed ? 'px-2 py-4' : 'p-4'}`}>
              {!isSidebarCollapsed && (
                <h2 className="text-base font-semibold mb-3 text-gray-300">Job List</h2>
              )}

              {/* Flat list of all jobs */}
              <div className="space-y-1">
                {isLoading ? (
                  <div className="text-gray-400 text-sm">Loading jobs...</div>
                ) : allJobs.length === 0 ? (
                  <div className="text-gray-400 text-sm">No jobs found</div>
                ) : (
                  allJobs.map(jobData => {
                    const job = jobDataToIJob(jobData);
                    return (
                      <JobListItem
                        key={job.id}
                        job={job}
                        isSelected={
                          selectedJob?.id === job.id || selectedJobId === job.id || jobId === job.id
                        }
                        onSelect={handleJobSelect}
                        isCollapsed={isSidebarCollapsed}
                      />
                    );
                  })
                )}
              </div>
            </div>
          </div>

          {/* Kaleido Logo at bottom */}
          <div className="p-4">
            <Image
              src="/images/logos/kaleido-logo-only.webp"
              alt="Kaleido"
              width={isSidebarCollapsed ? 40 : 56}
              height={isSidebarCollapsed ? 40 : 56}
              className="transition-all"
            />
          </div>
        </div>

        {/* Mobile Overlay */}
        {isMobileSidebarOpen && (
          <div
            className="fixed inset-0 bg-black/50 z-30 lg:hidden"
            onClick={() => setIsMobileSidebarOpen(false)}
          />
        )}

        {/* Right Content - Job Details */}
        <div className={`flex-1 overflow-hidden ${isSidebarCollapsed ? '' : ''} lg:pl-0 pl-0`}>
          {selectedJob ? (
            <UnifiedJobView jobId={jobId} embedded={true} />
          ) : (
            <div className="flex items-center justify-center h-full text-gray-400">
              <p>Select a job to view details</p>
            </div>
          )}
        </div>
      </div>
    </TooltipProvider>
  );
};
