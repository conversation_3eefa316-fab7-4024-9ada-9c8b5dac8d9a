// ============================================================================
// UNIFIED JOB STORE - Modular Implementation with Slices
// ============================================================================
// This store provides exact 1:1 compatibility with existing stores using
// a modular slice-based architecture for maintainability

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';

// Import slice creators
import {
  createJobOperationsSlice,
  createJobStateOperationsSlice,
  createJobWizardOperationsSlice,
  createJobsStoreOperationsSlice,
  createUploadJobsOperationsSlice,
  type UnifiedJobStoreSlices,
} from './slices';

// Import types and helpers
import { steps } from '@/contexts/jobs/constants';
import { JobStatus } from '@/entities';
import { getDefaultJob } from './helpers';
import type { JobFormData, JobStoreStats, UnifiedJobStoreState } from './types';

// ========== LocalStorage Initialization ==========
const getInitialUploadJobsState = () => {
  if (typeof window === 'undefined') return { jobs: {}, activeJobs: [] };

  try {
    const persistedJobs = localStorage.getItem('uploadJobs');
    const persistedActiveJobs = localStorage.getItem('uploadActiveJobs');

    return {
      jobs: persistedJobs ? JSON.parse(persistedJobs) : {},
      activeJobs: persistedActiveJobs ? JSON.parse(persistedActiveJobs) : [],
    };
  } catch (error) {
    console.error('Error loading upload jobs from localStorage:', error);
    return { jobs: {}, activeJobs: [] };
  }
};

// ============================================================================
// INITIAL STATE VALUES (matching existing stores exactly)
// ============================================================================

const initialFormData: JobFormData = {
  jobTitle: '',
  jobType: '',
  jobDescription: '',
  department: '',
  topCandidateThreshold: 0,
  secondTierCandidateThreshold: 0,
  requirements: '',
  status: JobStatus.NEW,
  location: '',
  employmentType: 'FULL_TIME',
  workMode: 'HYBRID',
  benefits: [],
};

const initialStats: JobStoreStats = {
  totalCandidates: 0,
  topTierCount: 0,
  secondTierCount: 0,
  othersCount: 0,
  unrankedCount: 0,
  shortlistedCount: 0,
  totalApplications: 0,
  viewCount: 0,
  publishedPlatformsCount: 0,
};

// ============================================================================
// UNIFIED STORE WITH SLICE COMPOSITION
// ============================================================================

export const useUnifiedJobStore = create<UnifiedJobStoreState & UnifiedJobStoreSlices>()(
  subscribeWithSelector((set, get) => {
    // Initialize upload jobs from localStorage
    const initialUploadState = getInitialUploadJobsState();

    return {
      // ============================================================================
      // INITIAL STATE - Exact match with existing stores
      // ============================================================================

      // Core data (dual format for compatibility)
      jobs: [], // Array format for jobStateStore
      jobsById: {}, // Object format for efficient lookups
      candidates: {}, // jobId -> MinimalCandidate[]
      allCandidatesFlat: [],
      workerJobs: initialUploadState.jobs,

      // Job Details & Stats
      currentJobDetails: null,
      stats: initialStats,
      matchRankCost: null,

      // UI state
      selectedJobId: null,
      currentJob: null,
      selectedJob: null, // Backward compatibility
      isLoading: false,
      isSaving: false,
      isPublishing: false,
      isUnpublishing: false,
      isProcessing: false,
      error: null,

      // Form state (jobStore compatibility)
      formData: initialFormData,
      originalFormData: null,
      hasUnsavedChanges: false,
      validationErrors: {},

      // Pagination & filtering
      currentPage: 1,
      pageSize: 20,
      totalPages: 0,
      filters: {},

      // JobStateStore compatibility
      lastUpdated: 0,
      jobUpdateTimestamps: {},

      // Cache management
      cache: {},
      lastFetchTime: {},
      cacheTimeout: 5 * 60 * 1000,

      // Upload job tracking
      isResumeUploadActive: false,
      activeJobs: initialUploadState.activeJobs,

      // Optimistic updates
      optimisticUpdates: {},
      silentUpdateInProgress: false,

      // Request deduplication
      pendingRequests: {},

      // Job creation wizard (the missing jobsStore functionality)
      job: getDefaultJob(),
      activeStep: 0,
      totalSteps: steps.length,
      isFinalStep: false,
      isPreview: false,
      fadeIn: true,
      steps,

      // Publishing
      availablePlatforms: [],
      selectedPlatforms: [],

      // Company management
      company: null,
      lastCompanyFetch: null,

      // Additional state
      isHydrated: false,
      userRole: null,
      jobsByStatus: {},
      jobsByStatusPagination: {
        currentPage: 1,
        totalPages: 1,
        totalItems: 0,
        itemsPerPage: 10,
      },
      lastJobsByStatusFetch: {},
      refreshingJobs: false,
      lastRefreshRequest: 0,
      refreshDebounceMs: 1000,

      // ============================================================================
      // SLICE COMPOSITION - Combine all store slices
      // ============================================================================

      // Job Operations Slice (Core CRUD, Forms, Publishing)
      ...createJobOperationsSlice(set, get),

      // Job State Operations Slice (Array management, JobStateStore compatibility)
      ...createJobStateOperationsSlice(set, get),

      // Upload Jobs Operations Slice (Worker tracking, UploadJobsStore compatibility)
      ...createUploadJobsOperationsSlice(set, get),

      // Job Wizard Operations Slice (Job creation wizard, the missing jobsStore)
      ...createJobWizardOperationsSlice(set, get),

      // Jobs Store Operations Slice (Additional jobsStore compatibility)
      ...createJobsStoreOperationsSlice(set, get),

      // All methods are implemented in the slices above
    };
  })
);

// ============================================================================
// BACKWARD COMPATIBILITY EXPORTS
// ============================================================================

// Custom hook for jobStore backward compatibility
const createJobStoreHook = () => {
  const hook = <T = any>(selector?: (state: UnifiedJobStoreState & UnifiedJobStoreSlices) => T) => {
    // If selector is provided, use it directly
    if (selector) {
      return useUnifiedJobStore(selector);
    }

    // If no selector, return the full store with overrides
    const store = useUnifiedJobStore();
    const selectedJobId = store.selectedJobId;

    // Get candidates for the currently selected job
    const candidates =
      selectedJobId && store.candidates[selectedJobId] ? store.candidates[selectedJobId] : null;

    // Return store with backward compatible structure
    return {
      ...store,
      candidates, // Override with single job's candidates
    };
  };

  // Lazy getters to avoid accessing store at module initialization
  Object.defineProperty(hook, 'subscribe', {
    get: () => useUnifiedJobStore.subscribe,
  });

  Object.defineProperty(hook, 'getState', {
    get: () => () => {
      const store = useUnifiedJobStore.getState();
      const selectedJobId = store.selectedJobId;

      // Get candidates for the currently selected job
      const candidates =
        selectedJobId && store.candidates[selectedJobId] ? store.candidates[selectedJobId] : null;

      return {
        ...store,
        candidates, // Override with single job's candidates
      };
    },
  });

  return hook;
};

export const useJobStore = createJobStoreHook();

// Main exports that replace the original stores
export const useJobStateStore = useUnifiedJobStore;
export const useJobsStore = useUnifiedJobStore;

// Special backward compatibility wrapper for uploadJobsStore
const createUploadJobsStoreHook = () => {
  const hook = () => {
    const store = useUnifiedJobStore();

    // Map workerJobs to jobs for backward compatibility
    return {
      jobs: store.workerJobs,
      activeJobs: store.activeJobs,
      addJob: store.addJob,
      updateJobStatus: store.updateJobStatus,
      updateJobProgress: store.updateJobProgress,
      removeJob: store.removeJob,
      clearCompletedJobs: store.clearCompletedJobs,
      getJobsByRelatedId: store.getJobsByRelatedId,
      getActiveJobsByType: store.getActiveJobsByType,
    };
  };

  // Lazy getters to avoid accessing store at module initialization
  Object.defineProperty(hook, 'getState', {
    get: () => () => {
      const store = useUnifiedJobStore.getState();
      return {
        jobs: store.workerJobs,
        activeJobs: store.activeJobs,
        addJob: store.addJob,
        updateJobStatus: store.updateJobStatus,
        updateJobProgress: store.updateJobProgress,
        removeJob: store.removeJob,
        clearCompletedJobs: store.clearCompletedJobs,
        getJobsByRelatedId: store.getJobsByRelatedId,
        getActiveJobsByType: store.getActiveJobsByType,
      };
    },
  });

  return hook;
};

export const useUploadJobsStore = createUploadJobsStoreHook();

// JobStateStore selectors for backward compatibility
export const useJobStateSelectors = {
  useJobs: () => useUnifiedJobStore(state => state.jobs),
  useSelectedJob: () => useUnifiedJobStore(state => state.selectedJob),
  useIsLoading: () => useUnifiedJobStore(state => state.isLoading),
  useIsSaving: () => useUnifiedJobStore(state => state.isSaving),
  useJobById: (jobId: string) => useUnifiedJobStore(state => state.getJobById(jobId)),
  useJobsByStatus: (status: string) =>
    useUnifiedJobStore(state => state.jobs.filter(job => job.status === status)),
  useRecentlyUpdatedJobs: (thresholdMs = 5000) =>
    useUnifiedJobStore(state => {
      const now = Date.now();
      return state.jobs.filter(job => {
        const lastUpdate = state.jobUpdateTimestamps[job.id];
        return lastUpdate && now - lastUpdate < thresholdMs;
      });
    }),
};

// Initialize the upload jobs service integration
if (typeof window !== 'undefined' && process.env.NODE_ENV !== 'test') {
  // Initialize service callbacks after store creation
  setTimeout(() => {
    const store = useUnifiedJobStore.getState();
    store.initializeUploadJobsService();
  }, 0);
}

// JobStateStore actions for backward compatibility
export const useJobStateActions = () =>
  useUnifiedJobStore(state => ({
    setJobs: state.setJobs,
    addJob: state.addJob,
    updateJob: state.updateJob,
    removeJob: state.removeJob,
    setSelectedJob: state.setSelectedJob,
    setIsLoading: state.setLoading,
    setIsSaving: state.setSaving,
    getJobById: state.getJobById,
    refreshJob: state.refreshJob,
    markJobAsUpdated: state.markJobAsUpdated,
    isJobRecentlyUpdated: state.isJobRecentlyUpdated,
    updateMultipleJobs: state.updateMultipleJobs,
    clearCache: state.clearCache,
    isDataStale: state.isDataStale,
  }));

// Debug utilities
export const debugJobStateStore = () => {
  const state = useUnifiedJobStore.getState();
  if (!Array.isArray(state.jobs)) {
    console.warn('⚠️ Jobs is not an array! Resetting store...');
    state.resetStore();
  }
};

// Make available globally in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as any).debugJobStateStore = debugJobStateStore;
  (window as any).resetJobStateStore = () => useUnifiedJobStore.getState().resetStore();
  (window as any).unifiedJobStore = useUnifiedJobStore;
}

// Export types for external use
export type {
  GroupedCandidates,
  JobData,
  JobFormData,
  JobStoreStats,
  MinimalCandidate,
  PublishPlatform,
  UnifiedJobStoreState,
  UploadJob,
} from './types';

// Export conversion utilities
export { jobDataArrayToIJobArray, jobDataToIJob } from './backward-compat';

// Export jobsStore compatibility items
export { defaultJob } from './helpers';

// Export utility functions for backward compatibility
export { clearJobsCacheByEndpoint, clearUserCaches } from './cache-utils';
