'use client';

import React, { useEffect, useState } from 'react';

import { motion } from 'framer-motion';

import CompaniesTable from '@/components/admin/CompaniesTable';
import UsageDashboard from '@/components/admin/UsageDashboard';
import ColorfulSmokeyOrbLoader from '@/components/Layouts/ColourfulLoader';
import AppLayout from '@/components/steps/layout/AppLayout';
import { apiClient as apiHelper } from '@/lib/apiHelper';
// Import the module resolver to ensure polyfills are applied
import { applyPolyfillsSafely } from '@/lib/module-resolver';
import { useJobsStore } from '@/stores/unifiedJobStore';

// Default stats data
const defaultStats = {
  totalCompanies: 0,
  totalJobs: 0,
  totalVideoJDs: 0,
  totalCandidates: 0,
  activeCompanies: 0,
  industryDistribution: [],
};

export default function UsagePage() {
  const [usageStats, setUsageStats] = useState(defaultStats);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [timeFilter, setTimeFilter] = useState<'daily' | 'weekly' | 'monthly' | 'yearly'>('weekly');
  const { refreshJobs, invalidateJobsCache } = useJobsStore();

  // Apply polyfills on component mount
  useEffect(() => {
    applyPolyfillsSafely();
  }, []);

  useEffect(() => {
    const fetchUsageStats = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Call the backend endpoint to get real usage statistics
        const response = await apiHelper.get('/dashboard/usage-stats');
        if (response && typeof response === 'object') {
          setUsageStats(response as any);
        } else {
          throw new Error('Invalid response format');
        }
      } catch (error: any) {
        console.error('Error fetching usage stats:', error);
        setError(error.message || 'Failed to fetch usage statistics');
        // Keep the default stats (all zeros) instead of using mock data
        setUsageStats(defaultStats);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUsageStats();
  }, []);

  const handleTimeFilterChange = (value: 'daily' | 'weekly' | 'monthly' | 'yearly') => {
    setTimeFilter(value);
  };

  return (
    <AppLayout>
      <div className="mx-auto w-full p-6">
        {isLoading ? (
          <ColorfulSmokeyOrbLoader text="Loading usage data..." useModalBg={false} />
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="space-y-6">
              {/* Time filter controls */}
              <div className="flex justify-end mb-4">
                <div className="flex items-center gap-2 bg-white/5 border border-white/10 p-1 rounded-md">
                  <button
                    onClick={() => handleTimeFilterChange('daily')}
                    className={`px-3 py-1 rounded-sm text-sm ${
                      timeFilter === 'daily' ? 'bg-purple-700/20 text-white' : 'text-white/70'
                    }`}
                  >
                    Day
                  </button>
                  <button
                    onClick={() => handleTimeFilterChange('weekly')}
                    className={`px-3 py-1 rounded-sm text-sm ${
                      timeFilter === 'weekly' ? 'bg-purple-700/20 text-white' : 'text-white/70'
                    }`}
                  >
                    Week
                  </button>
                  <button
                    onClick={() => handleTimeFilterChange('monthly')}
                    className={`px-3 py-1 rounded-sm text-sm ${
                      timeFilter === 'monthly' ? 'bg-purple-700/20 text-white' : 'text-white/70'
                    }`}
                  >
                    Month
                  </button>
                  <button
                    onClick={() => handleTimeFilterChange('yearly')}
                    className={`px-3 py-1 rounded-sm text-sm ${
                      timeFilter === 'yearly' ? 'bg-purple-700/20 text-white' : 'text-white/70'
                    }`}
                  >
                    Year
                  </button>
                </div>
              </div>

              {/* Error Display */}
              {error && (
                <div className="p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
                  <p className="text-red-400">Error loading usage statistics: {error}</p>
                  <button
                    onClick={() => {
                      // Refresh data through Zustand stores instead of full page reload
                      invalidateJobsCache();
                      refreshJobs(true);
                      // Clear error and retry loading
                      setError(null);
                      setIsLoading(true);
                    }}
                    className="mt-2 px-3 py-1 bg-red-500/20 text-red-300 rounded text-sm hover:bg-red-500/30 transition-colors"
                  >
                    Retry
                  </button>
                </div>
              )}

              {/* Usage Dashboard Stats - Always visible */}
              {usageStats ? (
                <UsageDashboard stats={usageStats} timeFilter={timeFilter} />
              ) : (
                !error && (
                  <div className="p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
                    <p className="text-yellow-400">No usage stats available</p>
                  </div>
                )
              )}

              {/* Companies Table */}
              <div className="mt-8">
                <h2 className="text-xl font-semibold text-white/90 mb-4">Company Usage Details</h2>
                <CompaniesTable />
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </AppLayout>
  );
}
